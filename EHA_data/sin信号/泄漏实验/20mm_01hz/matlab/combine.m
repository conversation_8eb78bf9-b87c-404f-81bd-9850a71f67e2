% data1 = load('005_1servo.mat','-ASCII');
% data2 = load('005_2servo.mat','-ASCII');
% % data3 = load('002_3servo.mat','-ASCII');
% % data4 = load('002_4servo.mat','-ASCII');
% % data5 = load('002_5servo.mat','-ASCII');
% 
% 
% cb_data1 = data1(1:18000);
% cb_data2 = data2(1:22000);
% % cb_data3 = data3(1:8000);
% % cb_data4 = data4(1:8000);
% % cb_data5 = data5(1:8000);
% combined_data = [cb_data1  cb_data2 ];
% 
% save('_005servo.mat', 'combined_data', '-v7.3');
% 
% % data = load('01AngleSample.mat','-ASCII');
% % save('1.mat', 'data', '-v7.3');

folder = 'E:\EHAshiyan\data\泄漏实验\20mm_01hz\matlab';  % 替换为实际的文件夹路径
filelist = dir(fullfile(folder, '*.mat'));
% 
for i = 1:numel(filelist)
    filename = fullfile(folder, filelist(i).name);
    
    % 加载MAT文件
    data = load(filename,'-ASCII');
    new_filename = fullfile(folder, ['_', filelist(i).name]);
    % 保存为较低版本的MAT文件
    save(new_filename, 'data', '-v7.3');
end

% folder = 'E:\EHAshiyan\data\泄漏实验\10mm_01hz\matlab\新建文件夹';  % 替换为实际的文件夹路径
% filelist = dir(fullfile(folder, '*.mat'));
% 
% % 创建一个空数组来存储提取的数据
% combined_data = [];
% 
% % 循环遍历每个MAT文件
% for i = 1:numel(filelist)
%     filename = fullfile(folder, filelist(i).name);
%     
%     % 加载MAT文件
%     data = load(filename);
%     
%     % 提取前100个数据
%     extracted_data = data.your_variable_name(1:100);  % 替换为实际的变量名
%     
%     % 将提取的数据拼接到新数组中
%     combined_data = [combined_data; extracted_data];
% end
% 
% % 保存新数组为MAT文件
% new_filename = 'path/to/save/combined_data.mat';  % 替换为实际的保存路径和文件名
% save(new_filename, 'combined_data');