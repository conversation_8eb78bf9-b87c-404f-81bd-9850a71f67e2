% data = load('01AngleSample.mat','-ASCII');
% save('1.mat', 'data', '-v7.3');

folder = 'E:\EHAshiyan\data\泄漏实验\20mm_02hz\matlab';  % 替换为实际的文件夹路径
filelist = dir(fullfile(folder, '*.mat'));

for i = 1:numel(filelist)
    filename = fullfile(folder, filelist(i).name);
    
    % 加载MAT文件
    data = load(filename);
    new_filename = fullfile(folder, [filelist(i).name]);
    % 保存为较低版本的MAT文件
    save(new_filename, 'data', '-v7');
end