# # train_dendritic_snn_epoch.py
#
# import torch
# import torch.nn as nn
# from torch.utils.data import Dataset, DataLoader
# import snntorch as snn
# from snntorch import surrogate
#
# import pandas as pd
# import numpy as np
# import os
# import glob
# from sklearn.model_selection import train_test_split
# from sklearn.preprocessing import MinMaxScaler
# from tqdm import tqdm
#
#
# # --- 1. 数据加载工具 (无变动) ---
# def load_and_window_data(data_dir, window_size, step):
#     all_files = glob.glob(os.path.join(data_dir, '*.csv'))
#     all_files.sort()
#     all_windows, all_labels = [], []
#     label_map = {os.path.basename(file).split('.csv')[0]: i for i, file in enumerate(all_files)}
#     for label_name, label_idx in label_map.items():
#         file_path = os.path.join(data_dir, f"{label_name}.csv")
#         df = pd.read_csv(file_path)
#         if df.shape[0] < window_size:
#             print(f"警告: 文件 '{file_path}' 的行数 ({df.shape[0]}) 少于 WINDOW_SIZE ({window_size})，将被跳过。")
#             continue
#         scaler = MinMaxScaler()
#         data_scaled = scaler.fit_transform(df)
#         for i in range(0, df.shape[0] - window_size + 1, step):
#             all_windows.append(data_scaled[i:i + window_size, :])
#             all_labels.append(label_idx)
#     return np.array(all_windows), np.array(all_labels), label_map
#
#
# class FaultDataset(Dataset):
#     def __init__(self, data, labels):
#         self.data = torch.tensor(data, dtype=torch.float32)
#         self.labels = torch.tensor(labels, dtype=torch.long)
#
#     def __len__(self):
#         return len(self.data)
#
#     def __getitem__(self, idx):
#         return self.data[idx], self.labels[idx]
#
#
# # --- 2. 新增：定义树突计算模块 ---
# class DendriticLayer(nn.Module):
#     """
#     一个实现树突计算的层。它将取代 nn.Linear。
#     每个神经元都有多个树突，每个树突处理一部分输入。
#     """
#
#     def __init__(self, input_size, output_size, num_dendrites):
#         super().__init__()
#         self.input_size = input_size
#         self.output_size = output_size
#         self.num_dendrites = num_dendrites
#
#         if self.input_size % self.num_dendrites != 0:
#             raise ValueError(f"input_size ({self.input_size}) 必须能被 num_dendrites ({self.num_dendrites}) 整除。")
#         self.inputs_per_dendrite = self.input_size // self.num_dendrites
#
#         # 权重形状: [输出神经元数, 树突数, 每个树突的输入数]
#         self.dendrite_weights = nn.Parameter(
#             torch.randn(self.output_size, self.num_dendrites, self.inputs_per_dendrite))
#         self.dendrite_biases = nn.Parameter(torch.randn(self.output_size, self.num_dendrites))
#         nn.init.kaiming_uniform_(self.dendrite_weights, a=np.sqrt(5))
#
#     def forward(self, x):
#         # x shape: [batch_size, input_size]
#         x_reshaped = x.view(-1, self.num_dendrites, self.inputs_per_dendrite)
#         # 树突局部计算 (线性 + 非线性)
#         dendritic_potentials = torch.einsum('bdi, odi -> bod', x_reshaped, self.dendrite_weights) + self.dendrite_biases
#         dendritic_activations = torch.sigmoid(dendritic_potentials)
#         # 在细胞体整合 (求和)
#         soma_current = torch.sum(dendritic_activations, dim=2)
#         return soma_current
#
#
# # --- 3. 修改：定义包含树突的SNN分类器 ---
# class DendriticSNNClassifier(nn.Module):
#     def __init__(self, input_size, hidden_size, num_classes, num_dendrites, beta=0.95):
#         super().__init__()
#         spike_grad = surrogate.atan()
#
#         # **修改点**: 使用DendriticLayer替换nn.Linear
#         self.dendritic1 = DendriticLayer(input_size, hidden_size, num_dendrites)
#         self.lif1 = snn.Leaky(beta=beta, spike_grad=spike_grad)
#
#         # 常规全连接层，用于最终分类 (保持不变)
#         self.fc_out = nn.Linear(hidden_size, num_classes)
#
#     def forward(self, x):
#         # x shape: [batch_size, window_size, input_size]
#         mem1 = self.lif1.init_leaky()
#         spk1_rec = []
#
#         for step in range(x.shape[1]):
#             # **修改点**: 将每个时间步的输入送入树突层
#             soma_current = self.dendritic1(x[:, step, :])
#             spk1, mem1 = self.lif1(soma_current, mem1)
#             spk1_rec.append(spk1)
#
#         feature_embedding = torch.stack(spk1_rec, dim=1).sum(dim=1)
#         out = self.fc_out(feature_embedding)
#         return out
#
#
# # --- 4. 训练与评估函数 (无变动) ---
# def train_epoch(model, train_loader, optimizer, criterion, device):
#     model.train()
#     running_loss = 0.0
#     correct_predictions = 0
#     total_samples = 0
#     for data, labels in train_loader:
#         data, labels = data.to(device), labels.to(device)
#         outputs = model(data)
#         loss = criterion(outputs, labels)
#         optimizer.zero_grad()
#         loss.backward()
#         optimizer.step()
#         running_loss += loss.item() * data.size(0)
#         _, predicted = torch.max(outputs.data, 1)
#         total_samples += labels.size(0)
#         correct_predictions += (predicted == labels).sum().item()
#     epoch_loss = running_loss / total_samples
#     epoch_acc = 100 * correct_predictions / total_samples
#     return epoch_loss, epoch_acc
#
#
# def evaluate_model(model, test_loader, criterion, device):
#     model.eval()
#     running_loss = 0.0;
#     correct_predictions = 0;
#     total_samples = 0
#     with torch.no_grad():
#         for data, labels in test_loader:
#             data, labels = data.to(device), labels.to(device)
#             outputs = model(data)
#             loss = criterion(outputs, labels)
#             running_loss += loss.item() * data.size(0)
#             _, predicted = torch.max(outputs.data, 1)
#             total_samples += labels.size(0)
#             correct_predictions += (predicted == labels).sum().item()
#     epoch_loss = running_loss / total_samples
#     epoch_acc = 100 * correct_predictions / total_samples
#     return epoch_loss, epoch_acc
#
#
# # --- 5. 主程序 (已修改) ---
# if __name__ == '__main__':
#     # --- [参数配置] ---
#     DATA_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\早期故障'
#     # DATA_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_10mm01hz全周期数据\all'
#     # DATA_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\all'
#     MODEL_SAVE_PATH = 'dendritic_snn_classifier_epoch.pth'  # **修改点**: 新的模型保存名
#
#     WINDOW_SIZE, STEP_SIZE = 1024, 1024
#     HIDDEN_SIZE = 128
#     BETA = 0.95
#     LEARNING_RATE = 0.001
#     NUM_EPOCHS = 100
#     BATCH_SIZE = 32
#
#     # **修改点**: 新增树突网络超参数
#     # 每个神经元的树突分支数量。
#     # 注意: 每个时间步的输入特征数 (即通道数) 必须能被此值整除。
#     NUM_DENDRITES = 7
#
#     # --- [数据加载与准备] ---
#     X, y, label_map = load_and_window_data(DATA_DIR, WINDOW_SIZE, STEP_SIZE)
#     if X.shape[0] == 0:
#         raise ValueError("未能加载任何有效数据，请检查路径、文件内容和WINDOW_SIZE设置。")
#     print(f"数据加载完成。总窗口数: {X.shape[0]}, 标签映射: {label_map}")
#
#     X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
#     train_dataset = FaultDataset(X_train, y_train)
#     test_dataset = FaultDataset(X_test, y_test)
#     train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
#     test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)
#
#     device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
#     print(f"使用设备: {device}")
#
#     # --- [模型、损失函数、优化器初始化] ---
#     INPUT_SIZE = X.shape[2]  # 这是每个时间步的输入特征数 (即通道数)
#     NUM_CLASSES = len(label_map)
#
#     # **修改点**: 检查输入维度是否与树突数兼容
#     if INPUT_SIZE % NUM_DENDRITES != 0:
#         raise ValueError(f"输入特征数 (通道数, {INPUT_SIZE}) 必须能被NUM_DENDRITES ({NUM_DENDRITES}) 整除。")
#
#     # **修改点**: 实例化新的树突SNN模型
#     model = DendriticSNNClassifier(
#         input_size=INPUT_SIZE,
#         hidden_size=HIDDEN_SIZE,
#         num_classes=NUM_CLASSES,
#         num_dendrites=NUM_DENDRITES,
#         beta=BETA
#     ).to(device)
#
#     criterion = nn.CrossEntropyLoss()
#     optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)
#
#     # --- [训练主循环] (无变动) ---
#     print("\n开始训练带有树突结构的SNN...")
#     for epoch in range(NUM_EPOCHS):
#         train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
#         test_loss, test_acc = evaluate_model(model, test_loader, criterion, device)
#
#         print(f"Epoch [{epoch + 1}/{NUM_EPOCHS}] | "
#               f"训练损失: {train_loss:.4f}, 训练准确率: {train_acc:.2f}% | "
#               f"测试损失: {test_loss:.4f}, 测试准确率: {test_acc:.2f}%")
#
#     # --- [保存模型] ---
#     torch.save(model.state_dict(), MODEL_SAVE_PATH)
#     print(f"\n训练完成。模型权重已保存至: '{MODEL_SAVE_PATH}'")


import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import snntorch as snn
from snntorch import surrogate

import pandas as pd
import numpy as np
import os
import glob
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from sklearn.utils.class_weight import compute_class_weight
from tqdm import tqdm


# --- 1. 修改后的数据加载函数 (只加载原始数据和窗口化) ---
def load_and_window_data(data_dir, window_size, step):
    """
    加载所有CSV文件，进行窗口化，但返回未经归一化的原始数据。
    """
    all_files = glob.glob(os.path.join(data_dir, '*.csv'))
    all_files.sort()
    windows, labels = [], []
    label_map = {os.path.basename(f).split('.csv')[0]: i for i, f in enumerate(all_files)}

    for file in all_files:
        df = pd.read_csv(file)
        if df.shape[0] < window_size:
            print(f"警告: 文件 '{file}' 的行数 ({df.shape[0]}) 少于 WINDOW_SIZE ({window_size})，将被跳过。")
            continue

        # 直接使用原始数据进行窗口化
        raw_data = df.values
        label_idx = label_map[os.path.basename(file).split('.csv')[0]]

        for i in range(0, raw_data.shape[0] - window_size + 1, step):
            windows.append(raw_data[i:i + window_size, :])
            labels.append(label_idx)

    return np.array(windows), np.array(labels), label_map


class FaultDataset(Dataset):
    # ... (无变动) ...
    def __init__(self, data, labels):
        self.data = torch.tensor(data, dtype=torch.float32)
        self.labels = torch.tensor(labels, dtype=torch.long)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return self.data[idx], self.labels[idx]


# --- 2. 树突层 (无变动) ---
class DendriticLayer(nn.Module):
    # ... (无变动) ...
    def __init__(self, input_size, output_size, num_dendrites):
        super().__init__()
        self.input_size = input_size
        self.output_size = output_size
        self.num_dendrites = num_dendrites
        print(f"DendriticLayer: input_size={input_size}, output_size={output_size}, num_dendrites={num_dendrites}")
        assert input_size % num_dendrites == 0, "input_size必须能被num_dendrites整除"
        self.inputs_per_dendrite = input_size // num_dendrites
        self.dendrite_weights = nn.Parameter(
            torch.randn(output_size, num_dendrites, self.inputs_per_dendrite))
        self.dendrite_biases = nn.Parameter(torch.randn(output_size, num_dendrites))
        nn.init.kaiming_uniform_(self.dendrite_weights, a=np.sqrt(5))

    def forward(self, x):
        x_reshaped = x.view(-1, self.num_dendrites, self.inputs_per_dendrite)
        dendritic_potentials = torch.einsum('bdi,odi->bod', x_reshaped, self.dendrite_weights) + self.dendrite_biases
        dendritic_activations = torch.sigmoid(dendritic_potentials)
        soma_current = torch.sum(dendritic_activations, dim=2)
        return soma_current


# --- 3. 改进版树突SNN分类器 (无变动) ---
class DendriticSNNClassifier(nn.Module):
    # ... (无变动) ...
    def __init__(self, input_size, hidden_size, num_classes, num_dendrites, beta=0.95, dropout_prob=0.3):
        super().__init__()
        spike_grad = surrogate.atan()
        self.dendritic1 = DendriticLayer(input_size, hidden_size, num_dendrites)
        self.bn1 = nn.BatchNorm1d(hidden_size)
        self.lif1 = snn.Leaky(beta=beta, spike_grad=spike_grad)
        self.dendritic2 = DendriticLayer(hidden_size, hidden_size, num_dendrites)
        self.bn2 = nn.BatchNorm1d(hidden_size)
        self.lif2 = snn.Leaky(beta=beta, spike_grad=spike_grad)
        self.dropout = nn.Dropout(dropout_prob)
        self.fc_out = nn.Linear(hidden_size, hidden_size)
        self.fc_out2 = nn.Linear(hidden_size, num_classes)

    def forward(self, x):
        mem1 = self.lif1.init_leaky()
        mem2 = self.lif2.init_leaky()
        spk2_rec = []
        for step in range(x.shape[1]):
            soma1 = self.dendritic1(x[:, step, :])
            soma1 = self.bn1(soma1)
            spk1, mem1 = self.lif1(soma1, mem1)
            soma2 = self.dendritic2(spk1)
            soma2 = self.bn2(soma2)
            spk2, mem2 = self.lif2(soma2, mem2)
            spk2_rec.append(spk2)
        embedding = torch.stack(spk2_rec, dim=1).sum(dim=1)
        embedding = self.dropout(embedding)
        out = self.fc_out(embedding)
        out = self.fc_out2(out)
        return out


# --- 4. 训练与评估函数 (无变动) ---
def train_epoch(model, train_loader, optimizer, criterion, device):
    # ... (无变动) ...
    model.train()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0
    for data, labels in train_loader:
        data, labels = data.to(device), labels.to(device)
        outputs = model(data)
        loss = criterion(outputs, labels)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        running_loss += loss.item() * data.size(0)
        _, predicted = torch.max(outputs.data, 1)
        total_samples += labels.size(0)
        correct_predictions += (predicted == labels).sum().item()
    epoch_loss = running_loss / total_samples
    epoch_acc = 100 * correct_predictions / total_samples
    return epoch_loss, epoch_acc


def evaluate_model(model, test_loader, criterion, device):
    # ... (无变动) ...
    model.eval()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0
    with torch.no_grad():
        for data, labels in test_loader:
            data, labels = data.to(device), labels.to(device)
            outputs = model(data)
            loss = criterion(outputs, labels)
            running_loss += loss.item() * data.size(0)
            _, predicted = torch.max(outputs.data, 1)
            total_samples += labels.size(0)
            correct_predictions += (predicted == labels).sum().item()
    epoch_loss = running_loss / total_samples
    epoch_acc = 100 * correct_predictions / total_samples
    return epoch_loss, epoch_acc


# --- 5. 主程序 (已修改) ---
if __name__ == '__main__':
    # --- 参数配置 ---
    DATA_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_10mm01hz全周期数据\前期故障'
    MODEL_SAVE_PATH = 'dendritic_snn_classifier_epoch_v2.pth'
    WINDOW_SIZE, STEP_SIZE = 1024, 1024
    HIDDEN_SIZE = 7 * 50
    BETA = 0.95
    LEARNING_RATE = 0.01
    NUM_EPOCHS = 100
    BATCH_SIZE = 32
    NUM_DENDRITES = 7
    DROPOUT_PROB = 0.3

    # --- 数据加载与准备 ---
    # **修改点 1**: 加载原始数据 (未归一化)
    X_raw, y, label_map = load_and_window_data(DATA_DIR, WINDOW_SIZE, STEP_SIZE)
    if X_raw.shape[0] == 0:
        raise ValueError("未能加载任何有效数据，请检查路径、文件内容和WINDOW_SIZE设置。")
    print(f"原始数据加载完成。总窗口数: {X_raw.shape[0]}, 标签映射: {label_map}")

    # **修改点 2**: 先划分训练集和测试集
    X_train_raw, X_test_raw, y_train, y_test = train_test_split(X_raw, y, test_size=0.4, random_state=42, stratify=y)

    # **修改点 3**: 执行正确的归一化流程
    print("正在执行归一化...")
    # 获取数据维度
    n_samples_train, n_steps, n_features = X_train_raw.shape
    n_samples_test = X_test_raw.shape[0]

    # 初始化归一化器
    scaler = MinMaxScaler()

    # Reshape数据为2D [样本*时间步, 特征数]，以便scaler处理
    X_train_reshaped = X_train_raw.reshape(-1, n_features)

    # 仅在训练数据上 fit 或 fit_transform
    scaler.fit(X_train_reshaped)
    X_train_scaled = scaler.transform(X_train_reshaped)

    # 在测试数据上只用 transform
    X_test_reshaped = X_test_raw.reshape(-1, n_features)
    X_test_scaled = scaler.transform(X_test_reshaped)

    # 将数据Reshape回原来的3D形状 [样本, 时间步, 特征数]
    X_train = X_train_scaled.reshape(n_samples_train, n_steps, n_features)
    X_test = X_test_scaled.reshape(n_samples_test, n_steps, n_features)
    print("归一化完成。")

    # 创建PyTorch Dataset和DataLoader
    train_dataset = FaultDataset(X_train, y_train)
    test_dataset = FaultDataset(X_test, y_test)
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # --- 检查参数合法性 ---
    INPUT_SIZE = X_train.shape[2]
    NUM_CLASSES = len(label_map)
    assert INPUT_SIZE % NUM_DENDRITES == 0, f"输入特征数 ({INPUT_SIZE}) 必须能被NUM_DENDRITES ({NUM_DENDRITES}) 整除"

    # --- 类别加权损失 ---
    class_weights = compute_class_weight("balanced", classes=np.unique(y_train), y=y_train)
    class_weights = torch.tensor(class_weights, dtype=torch.float32).to(device)
    criterion = nn.CrossEntropyLoss(weight=class_weights)

    # --- 初始化模型 ---
    model = DendriticSNNClassifier(
        input_size=INPUT_SIZE,
        hidden_size=HIDDEN_SIZE,
        num_classes=NUM_CLASSES,
        num_dendrites=NUM_DENDRITES,
        beta=BETA,
        dropout_prob=DROPOUT_PROB
    ).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE, weight_decay=1e-4)

    # --- 训练主循环 ---
    print("\n开始训练带有树突结构的SNN...")
    for epoch in range(NUM_EPOCHS):
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        test_loss, test_acc = evaluate_model(model, test_loader, criterion, device)
        print(f"Epoch [{epoch + 1}/{NUM_EPOCHS}] | "
              f"训练损失: {train_loss:.4f}, 训练准确率: {train_acc:.2f}% | "
              f"测试损失: {test_loss:.4f}, 测试准确率: {test_acc:.2f}%")

    torch.save(model.state_dict(), MODEL_SAVE_PATH)
    print(f"\n训练完成。模型权重已保存至: '{MODEL_SAVE_PATH}'")