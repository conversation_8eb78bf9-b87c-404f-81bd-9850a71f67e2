# train_snn_epoch.py

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import snntorch as snn
from snntorch import surrogate

import pandas as pd
import numpy as np
import os
import glob
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from tqdm import tqdm


# --- 1. 数据加载工具 ---
def load_and_window_data(data_dir, window_size, step):
    """
    从CSV文件夹加载数据，并应用滑动窗口。
    每个CSV文件被视为一个类别。
    """
    all_files = glob.glob(os.path.join(data_dir, '*.csv'))
    all_files.sort()  # 保证每次加载的标签映射顺序一致
    all_windows, all_labels = [], []

    label_map = {os.path.basename(file).split('.csv')[0]: i for i, file in enumerate(all_files)}

    for label_name, label_idx in label_map.items():
        file_path = os.path.join(data_dir, f"{label_name}.csv")
        df = pd.read_csv(file_path)

        if df.shape[0] < window_size:
            print(f"警告: 文件 '{file_path}' 的行数 ({df.shape[0]}) 少于 WINDOW_SIZE ({window_size})，将被跳过。")
            continue

        scaler = MinMaxScaler()
        data_scaled = scaler.fit_transform(df)

        for i in range(0, df.shape[0] - window_size + 1, step):
            all_windows.append(data_scaled[i:i + window_size, :])
            all_labels.append(label_idx)

    return np.array(all_windows), np.array(all_labels), label_map


class FaultDataset(Dataset):
    """
    标准PyTorch数据集类，用于被DataLoader封装。
    """

    def __init__(self, data, labels):
        self.data = torch.tensor(data, dtype=torch.float32)
        self.labels = torch.tensor(labels, dtype=torch.long)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return self.data[idx], self.labels[idx]




# --- 2. SNN 分类器模型 ---
class SNNClassifier(nn.Module):
    def __init__(self, input_size, hidden_size, num_classes, beta=0.95):
        super().__init__()
        spike_grad = surrogate.atan()

        # SNN部分，用于特征提取
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.lif1 = snn.Leaky(beta=beta, spike_grad=spike_grad)

        # 常规全连接层，用于最终分类
        self.fc_out = nn.Linear(hidden_size, num_classes)

    def forward(self, x):
        # x shape: [batch_size, window_size, input_size]

        # 初始化脉冲神经元的膜电位
        mem1 = self.lif1.init_leaky()

        spk1_rec = []
        # SNN的时间步循环
        for step in range(x.shape[1]):
            cur1 = self.fc1(x[:, step, :])
            spk1, mem1 = self.lif1(cur1, mem1)
            spk1_rec.append(spk1)

        # 将所有时间步的脉冲相加，作为时间序列的特征表示
        feature_embedding = torch.stack(spk1_rec, dim=1).sum(dim=1)

        # 将特征输入到常规的分类头，得到最终的分类logits
        out = self.fc_out(feature_embedding)
        return out


# --- 3. 训练与评估函数 ---
def train_epoch(model, train_loader, optimizer, criterion, device):
    """
    执行一个完整的训练Epoch。
    """
    model.train()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0

    # 使用tqdm显示进度条
    for data, labels in train_loader:
        data, labels = data.to(device), labels.to(device)

        # 前向传播
        outputs = model(data)
        loss = criterion(outputs, labels)

        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        # 统计损失和准确率
        running_loss += loss.item() * data.size(0)
        _, predicted = torch.max(outputs.data, 1)
        total_samples += labels.size(0)
        correct_predictions += (predicted == labels).sum().item()

    epoch_loss = running_loss / total_samples
    epoch_acc = 100 * correct_predictions / total_samples
    return epoch_loss, epoch_acc


def evaluate_model(model, test_loader, criterion, device):
    """
    在测试集上评估模型性能。
    """
    model.eval()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0

    with torch.no_grad():  # 在评估时，不计算梯度
        for data, labels in test_loader:
            data, labels = data.to(device), labels.to(device)

            outputs = model(data)
            loss = criterion(outputs, labels)

            running_loss += loss.item() * data.size(0)
            _, predicted = torch.max(outputs.data, 1)
            total_samples += labels.size(0)
            correct_predictions += (predicted == labels).sum().item()

    epoch_loss = running_loss / total_samples
    epoch_acc = 100 * correct_predictions / total_samples
    return epoch_loss, epoch_acc


# --- 主程序 ---
if __name__ == '__main__':
    # --- [参数配置] ---
    DATA_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\早期故障'
    MODEL_SAVE_PATH = 'snn_classifier_epoch.pth'

    WINDOW_SIZE, STEP_SIZE = 1024, 1024
    HIDDEN_SIZE = 128
    BETA = 0.95
    LEARNING_RATE = 0.001

    # Epoch式训练的关键参数
    NUM_EPOCHS = 100  # 总共训练的轮数
    BATCH_SIZE = 32  # 每个批次包含的样本数

    # --- [数据加载与准备] ---
    X, y, label_map = load_and_window_data(DATA_DIR, WINDOW_SIZE, STEP_SIZE)
    if X.shape[0] == 0:
        raise ValueError("未能从数据目录加载任何有效数据，请检查路径、文件内容和WINDOW_SIZE设置。")
    print(f"数据加载完成。总窗口数: {X.shape[0]}, 标签映射: {label_map}")

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    # 创建PyTorch Dataset
    train_dataset = FaultDataset(X_train, y_train)
    test_dataset = FaultDataset(X_test, y_test)

    # 创建PyTorch DataLoader
    # shuffle=True 对训练数据进行打乱，这对于epoch式训练至关重要
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # --- [模型、损失函数、优化器初始化] ---
    INPUT_SIZE = X.shape[2]
    NUM_CLASSES = len(label_map)

    model = SNNClassifier(input_size=INPUT_SIZE, hidden_size=HIDDEN_SIZE, num_classes=NUM_CLASSES, beta=BETA).to(device)

    # 标准的交叉熵损失函数，适用于多分类任务
    criterion = nn.CrossEntropyLoss()

    optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)

    # --- [训练主循环] ---
    print("\n开始Epoch式训练...")
    for epoch in range(NUM_EPOCHS):
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        test_loss, test_acc = evaluate_model(model, test_loader, criterion, device)

        print(f"Epoch [{epoch + 1}/{NUM_EPOCHS}] | "
              f"训练损失: {train_loss:.4f}, 训练准确率: {train_acc:.2f}% | "
              f"测试损失: {test_loss:.4f}, 测试准确率: {test_acc:.2f}%")

    # --- [保存模型] ---
    torch.save(model.state_dict(), MODEL_SAVE_PATH)
    print(f"\n训练完成。模型权重已保存至: '{MODEL_SAVE_PATH}'")