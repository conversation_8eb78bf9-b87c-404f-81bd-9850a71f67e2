# train_multilayer_snn_epoch_v6_final.py

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import snntorch as snn
from snntorch import surrogate
from snntorch import utils

import pandas as pd
import numpy as np
import os
import glob
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from tqdm import tqdm


# --- [数据加载工具] (无变动) ---
def load_and_window_data(data_dir, window_size, step):
    all_files = glob.glob(os.path.join(data_dir, '*.csv'));
    all_files.sort()
    all_windows, all_labels = [], [];
    label_map = {os.path.basename(file).split('.csv')[0]: i for i, file in enumerate(all_files)}
    for label_name, label_idx in label_map.items():
        file_path = os.path.join(data_dir, f"{label_name}.csv");
        df = pd.read_csv(file_path)
        if df.shape[0] < window_size: print(f"警告: 文件 '{file_path}' 行数不足，将被跳过。");continue
        scaler = MinMaxScaler();
        data_scaled = scaler.fit_transform(df)
        for i in range(0, df.shape[0] - window_size + 1, step): all_windows.append(
            data_scaled[i:i + window_size, :]);all_labels.append(label_idx)
    return np.array(all_windows), np.array(all_labels), label_map


class FaultDataset(Dataset):
    def __init__(self, data, labels): self.data = torch.tensor(data, dtype=torch.float32);self.labels = torch.tensor(
        labels, dtype=torch.long)

    def __len__(self): return len(self.data)

    def __getitem__(self, idx): return self.data[idx], self.labels[idx]


# ==========================================================
#      1. 恢复并“升级”Izhikevich神经元 (核心修正)
# ==========================================================
class IzhikevichNeuron(snn.SpikingNeuron):
    def __init__(self, a=0.02, b=0.2, c=-65, d=8, reset_mechanism="subtract", spike_grad=None):
        super().__init__(threshold=30.0, spike_grad=spike_grad, reset_mechanism=reset_mechanism)

        self.a = a
        self.b = b
        self.c = c
        self.d = d

        # 移除空的buffer注册，改为在forward中动态初始化
        self.v = None
        self.u = None

    def init_mem(self, batch_size, hidden_size, device):
        """初始化膜电位状态"""
        self.v = torch.full((batch_size, hidden_size), self.c, device=device)
        self.u = torch.zeros((batch_size, hidden_size), device=device)
        return self.v, self.u

    def forward(self, x):
        # 动态初始化状态变量
        if self.v is None or self.v.shape != x.shape:
            self.v, self.u = self.init_mem(x.shape[0], x.shape[1], x.device)

        # 更新 v 和 u
        dv = 0.04 * self.v ** 2 + 5 * self.v + 140 - self.u + x
        du = self.a * (self.b * self.v - self.u)

        self.v = self.v + dv
        self.u = self.u + du

        # 判断脉冲
        spk = self.fire(self.v)

        # 重置
        self.reset_mem(spk)

        return spk

    def reset_mem(self, spk):
        """在发放脉冲后如何重置状态"""
        self.v = torch.where(spk > 0, torch.full_like(self.v, self.c), self.v)
        self.u = torch.where(spk > 0, self.u + self.d, self.u)


# ==========================================================
#              2. 多神经元层 (现在可以正确工作)
# ==========================================================
class MultiNeuronLayer(nn.Module):
    def __init__(self, input_size, hidden_size, beta_lif, beta_alpha, alpha_val):
        super().__init__()
        spike_grad = surrogate.atan()

        self.fc_lif = nn.Linear(input_size, hidden_size)
        self.lif = snn.Leaky(beta=beta_lif, spike_grad=spike_grad)

        # **修正点 4**: 现在我们使用自己定义的、与snnTorch兼容的Izhikevich神经元
        self.fc_izh = nn.Linear(input_size, hidden_size)
        self.izh = IzhikevichNeuron(spike_grad=spike_grad)

        self.fc_alpha = nn.Linear(input_size, hidden_size)
        self.alpha = snn.Alpha(beta=beta_alpha, alpha=alpha_val, spike_grad=spike_grad)

        self.fusion_layer = nn.Linear(hidden_size * 3, hidden_size)
        self.soma_lif = snn.Leaky(beta=beta_lif, spike_grad=spike_grad)

    def forward(self, x):
        spk_lif = self.lif(self.fc_lif(x))
        spk_izh = self.izh(self.fc_izh(x))
        spk_alpha = self.alpha(self.fc_alpha(x))

        fused_spikes = torch.cat([spk_lif, spk_izh, spk_alpha], dim=1)
        soma_current = self.fusion_layer(fused_spikes)
        output_spikes = self.soma_lif(soma_current)

        return output_spikes


# ==========================================================
#              3. 多层SNN分类器 (无变动)
# ==========================================================
class MultiLayerSNNClassifier(nn.Module):
    def __init__(self, input_size, hidden_size, num_classes, num_layers=2, dropout_prob=0.3,
                 beta_lif=0.95, beta_alpha=0.8, alpha_val=0.9):
        super().__init__()
        self.layers = nn.ModuleList()
        self.layers.append(MultiNeuronLayer(input_size, hidden_size, beta_lif, beta_alpha, alpha_val))
        for _ in range(num_layers - 1):
            self.layers.append(MultiNeuronLayer(hidden_size, hidden_size, beta_lif, beta_alpha, alpha_val))
        self.dropout = nn.Dropout(dropout_prob)
        self.fc_out = nn.Linear(hidden_size, num_classes)

    def forward(self, x):
        utils.reset(self)
        last_layer_spikes = []
        for step in range(x.shape[1]):
            current_spikes = x[:, step, :]
            for layer in self.layers:
                current_spikes = layer(current_spikes)
            last_layer_spikes.append(current_spikes)
        embedding = torch.stack(last_layer_spikes, dim=1).sum(dim=1)
        embedding = self.dropout(embedding)
        out = self.fc_out(embedding)
        return out


# --- [训练与评估函数] (无变动) ---
def train_epoch(model, train_loader, optimizer, criterion, device):
    model.train();
    running_loss = 0.0;
    correct_predictions = 0;
    total_samples = 0
    pbar = tqdm(train_loader, desc="训练中")
    for data, labels in pbar:
        data, labels = data.to(device), labels.to(device)
        outputs = model(data);
        loss = criterion(outputs, labels)
        optimizer.zero_grad();
        loss.backward();
        optimizer.step()
        running_loss += loss.item() * data.size(0);
        _, predicted = torch.max(outputs.data, 1)
        total_samples += labels.size(0);
        correct_predictions += (predicted == labels).sum().item()
    return running_loss / total_samples, 100 * correct_predictions / total_samples


def evaluate_model(model, test_loader, criterion, device):
    model.eval();
    running_loss = 0.0;
    correct_predictions = 0;
    total_samples = 0
    with torch.no_grad():
        for data, labels in test_loader:
            data, labels = data.to(device), labels.to(device)
            outputs = model(data);
            loss = criterion(outputs, labels)
            running_loss += loss.item() * data.size(0);
            _, predicted = torch.max(outputs.data, 1)
            total_samples += labels.size(0);
            correct_predictions += (predicted == labels).sum().item()
    return running_loss / total_samples, 100 * correct_predictions / total_samples


# ==========================================================
#                        4. 主程序 (无变动)
# ==========================================================
if __name__ == '__main__':
    data_dir = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\早期故障';
    model_save_path = 'multilayer_snn_classifier.pth'
    window_size, step_size = 1024, 1024;
    hidden_size = 128;
    learning_rate = 1e-3;
    num_epochs = 60;
    batch_size = 32;
    dropout_prob = 0.3;
    num_layers = 2
    beta_lif = 0.95;
    beta_alpha = 0.85;
    alpha_val = 0.9
    X, y, label_map = load_and_window_data(data_dir, window_size, step_size)
    if X.shape[0] == 0: raise ValueError("未能加载任何有效数据。")
    print(f"数据加载完成。总窗口数: {X.shape[0]}, 标签映射: {label_map}")
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    train_dataset = FaultDataset(X_train, y_train);
    test_dataset = FaultDataset(X_test, y_test)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True);
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu");
    print(f"使用设备: {device}")
    input_size = X.shape[2];
    num_classes = len(label_map)
    model = MultiLayerSNNClassifier(input_size=input_size, hidden_size=hidden_size, num_classes=num_classes,
                                    num_layers=num_layers, dropout_prob=dropout_prob, beta_lif=beta_lif,
                                    beta_alpha=beta_alpha, alpha_val=alpha_val).to(device)
    criterion = nn.CrossEntropyLoss();
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    print(f"\n开始训练 {num_layers}-层 多神经元SNN...")
    for epoch in range(num_epochs):
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        test_loss, test_acc = evaluate_model(model, test_loader, criterion, device)
        print(
            f"Epoch [{epoch + 1}/{num_epochs}] | 训练损失: {train_loss:.4f}, 训练准确率: {train_acc:.2f}% | 测试损失: {test_loss:.4f}, 测试准确率: {test_acc:.2f}%")
    torch.save(model.state_dict(), model_save_path);
    print(f"\n训练完成。模型权重已保存至: '{model_save_path}'")