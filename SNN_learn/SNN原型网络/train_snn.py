# train_snn.py

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import snntorch as snn
from snntorch import surrogate

import pandas as pd
import numpy as np
import os
import glob
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
import random
from collections import defaultdict
from tqdm import tqdm

# --- 数据加载工具 (与之前相同) ---
def load_and_window_data(data_dir, window_size, step):
    all_files = glob.glob(os.path.join(data_dir, '*.csv'))
    all_windows, all_labels = [], []
    label_map = {os.path.basename(file).split('.csv')[0]: i for i, file in enumerate(all_files)}
    for label_name, label_idx in label_map.items():
        file_path = os.path.join(data_dir, f"{label_name}.csv")
        df = pd.read_csv(file_path)
        scaler = MinMaxScaler()
        data_scaled = scaler.fit_transform(df)
        for i in range(0, df.shape[0] - window_size + 1, step):
            all_windows.append(data_scaled[i:i + window_size, :])
            all_labels.append(label_idx)
    return np.array(all_windows), np.array(all_labels), label_map


# --- SNN 编码器模型 (与之前相同) ---
class SNNEncoder(nn.Module):
    def __init__(self, input_size, hidden_size, beta=0.95):
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        spike_grad = surrogate.atan()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.lif1 = snn.Leaky(beta=beta, spike_grad=spike_grad)

    def forward(self, x):
        mem1 = self.lif1.init_leaky()
        spk1_rec = []
        for step in range(x.shape[1]):
            cur1 = self.fc1(x[:, step, :])
            spk1, mem1 = self.lif1(cur1, mem1)
            spk1_rec.append(spk1)
        embedding = torch.stack(spk1_rec, dim=1).sum(dim=1)
        return embedding


# --- 原型网络训练逻辑 (与之前相同) ---
def run_prototypical_task(model, data, labels, n_way, k_shot, is_train=True):
    class_indices = defaultdict(list)
    for i, label in enumerate(labels):
        class_indices[label.item()].append(i)
    unique_labels = list(class_indices.keys())
    task_classes = random.sample(unique_labels, n_way)

    support_indices, query_indices = [], []
    for class_id in task_classes:
        sampled_indices = np.random.choice(class_indices[class_id], k_shot, replace=True)
        support_indices.extend(sampled_indices)
        query_indices.extend(sampled_indices)

    X_support, y_support = data[support_indices], labels[support_indices]
    X_query, y_query = data[query_indices], labels[query_indices]

    label_mapper = {gl: ll for ll, gl in enumerate(task_classes)}
    y_support_local = torch.tensor([label_mapper[l.item()] for l in y_support], device=data.device)
    y_query_local = torch.tensor([label_mapper[l.item()] for l in y_query], device=data.device)

    if is_train:
        model.train()
    else:
        model.eval()

    support_embeddings = model(X_support)
    prototypes = torch.stack([support_embeddings[y_support_local == i].mean(dim=0) for i in range(n_way)])

    query_embeddings = model(X_query)
    dists = -torch.cdist(query_embeddings, prototypes)
    loss = F.cross_entropy(dists, y_query_local)

    _, predicted = torch.max(dists, 1)
    acc = (predicted == y_query_local).float().mean().item() * 100

    return loss, acc


# --- 主训练程序 ---
# if __name__ == '__main__':
#     # --- 超参数 ---
#     TRAIN_DATA_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\早期故障'
#     MODEL_SAVE_PATH = 'snn_encoder.pth'  # 模型保存路径
#
#     WINDOW_SIZE, STEP_SIZE = 500, 500
#     HIDDEN_SIZE = 128
#     BETA = 0.95
#     LEARNING_RATE = 1e-3
#
#     NUM_TASKS = 200
#     N_WAY = 5
#     K_SHOT = 4
#     EVAL_TASKS = 100
#     EVAL_INTERVAL = 100
#
#     # --- 数据加载与准备 ---
#     X, y, label_map = load_and_window_data(TRAIN_DATA_DIR, WINDOW_SIZE, STEP_SIZE)
#     print(f"数据加载完成。总窗口数: {X.shape[0]}, 标签映射: {label_map}")
#
#     X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
#
#     device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
#     print(f"使用设备: {device}")
#
#     X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(device)
#     y_train_tensor = torch.tensor(y_train, dtype=torch.long).to(device)
#     X_test_tensor = torch.tensor(X_test, dtype=torch.float32).to(device)
#     y_test_tensor = torch.tensor(y_test, dtype=torch.long).to(device)
#
#     # --- 模型初始化与训练 ---
#     INPUT_SIZE = X.shape[2]  # 获取通道数
#     model = SNNEncoder(input_size=INPUT_SIZE, hidden_size=HIDDEN_SIZE, beta=BETA).to(device)
#     optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)
#
#     print(f"\n开始原型网络训练 ({N_WAY}-way, {K_SHOT}-shot)...")
#
#     for task_num in tqdm(range(NUM_TASKS), desc="模型训练进度"):
#         loss, acc = run_prototypical_task(model, X_train_tensor, y_train_tensor, N_WAY, K_SHOT, is_train=True)
#
#         optimizer.zero_grad()
#         loss.backward()
#         optimizer.step()
#
#         if (task_num + 1) % EVAL_INTERVAL == 0:
#             model.eval()
#             with torch.no_grad():
#                 test_losses, test_accs = [], []
#                 for _ in range(EVAL_TASKS):
#                     test_loss, test_acc = run_prototypical_task(model, X_test_tensor, y_test_tensor, N_WAY, K_SHOT,
#                                                                 is_train=False)
#                     test_losses.append(test_loss.item())
#                     test_accs.append(test_acc)
#
#             avg_test_loss = np.mean(test_losses)
#             avg_test_acc = np.mean(test_accs)
#
#             print(f"任务 [{task_num + 1}/{NUM_TASKS}] | "
#                   f"当前训练损失: {loss.item():.4f}, 训练准确率: {acc:.2f}% | "
#                   f"平均测试损失: {avg_test_loss:.4f}, 平均测试准确率: {avg_test_acc:.2f}%")
#
#     # --- 保存模型 ---
#     torch.save(model.state_dict(), MODEL_SAVE_PATH)
#     print(f"\n训练完成。模型权重已保存至: '{MODEL_SAVE_PATH}'")

if __name__ == '__main__':
    # --- 超参数 ---
    TRAIN_DATA_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\早期故障'
    MODEL_SAVE_PATH = 'snn_encoder.pth'  # 模型保存路径

    WINDOW_SIZE, STEP_SIZE = 1024, 1024
    HIDDEN_SIZE = 128
    BETA = 0.95
    LEARNING_RATE = 1e-3

    NUM_TASKS = 200
    N_WAY = 8   # 学几种例句
    K_SHOT = 8  # 分辨的时候参考几个例句
    EVAL_TASKS = 100
    EVAL_INTERVAL = 100

    # --- 数据加载与准备 ---
    X, y, label_map = load_and_window_data(TRAIN_DATA_DIR, WINDOW_SIZE, STEP_SIZE)
    print(f"数据加载完成。总窗口数: {X.shape[0]}, 标签映射: {label_map}")

    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train_tensor = torch.tensor(y_train, dtype=torch.long).to(device)
    X_test_tensor = torch.tensor(X_test, dtype=torch.float32).to(device)
    y_test_tensor = torch.tensor(y_test, dtype=torch.long).to(device)

    # --- 模型初始化与训练 ---
    INPUT_SIZE = X.shape[2]  # 获取通道数
    model = SNNEncoder(input_size=INPUT_SIZE, hidden_size=HIDDEN_SIZE, beta=BETA).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)

    print(f"\n开始原型网络训练 ({N_WAY}-way, {K_SHOT}-shot)...")

    # <--- 2. 在主循环中包裹 tqdm ---
    # desc 参数为进度条添加了描述性文字
    for task_num in tqdm(range(NUM_TASKS), desc="模型训练进度"):
        loss, acc = run_prototypical_task(model, X_train_tensor, y_train_tensor, N_WAY, K_SHOT, is_train=True)

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        # 评估逻辑保持不变
        if (task_num + 1) % EVAL_INTERVAL == 0:
            model.eval()
            with torch.no_grad():
                test_losses, test_accs = [], []
                # (可选) 也可以为评估循环添加一个内部进度条
                # for _ in tqdm(range(EVAL_TASKS), desc="正在评估", leave=False):
                for _ in range(EVAL_TASKS):
                    test_loss, test_acc = run_prototypical_task(model, X_test_tensor, y_test_tensor, N_WAY, K_SHOT,
                                                                is_train=False)
                    test_losses.append(test_loss.item())
                    test_accs.append(test_acc)

            avg_test_loss = np.mean(test_losses)
            avg_test_acc = np.mean(test_accs)

            # tqdm 会自动处理打印语句，确保进度条不会被打乱
            tqdm.write(f"\n任务 [{task_num + 1}/{NUM_TASKS}] | "
                       f"当前训练损失: {loss.item():.4f}, 训练准确率: {acc:.2f}% | "
                       f"平均测试损失: {avg_test_loss:.4f}, 平均测试准确率: {avg_test_acc:.2f}%")

    # --- 保存模型 ---
    torch.save(model.state_dict(), MODEL_SAVE_PATH)
    print(f"\n训练完成。模型权重已保存至: '{MODEL_SAVE_PATH}'")