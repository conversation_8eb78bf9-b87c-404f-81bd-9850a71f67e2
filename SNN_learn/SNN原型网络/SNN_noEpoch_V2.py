import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import snntorch as snn
from snntorch import surrogate

import pandas as pd
import numpy as np
import os
import glob
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
import random
from collections import defaultdict


# --- 1. 数据加载与预处理 (无变动) ---
def load_and_window_data(data_dir, window_size, step):
    all_files = glob.glob(os.path.join(data_dir, '*.csv'))
    all_windows, all_labels = [], []
    # 如果您的标签是从1开始的，请使用 enumerate(all_files, 1)
    label_map = {os.path.basename(file).split('.csv')[0]: i for i, file in enumerate(all_files)}
    for label_name, label_idx in label_map.items():
        file_path = os.path.join(data_dir, f"{label_name}.csv")
        df = pd.read_csv(file_path)
        scaler = MinMaxScaler()
        data_scaled = scaler.fit_transform(df)
        for i in range(0, df.shape[0] - window_size + 1, step):
            all_windows.append(data_scaled[i:i + window_size, :])
            all_labels.append(label_idx)
    return np.array(all_windows), np.array(all_labels), label_map


# --- 2. SNN 编码器模型 (无变动) ---
class SNNEncoder(nn.Module):
    def __init__(self, input_size, hidden_size, beta=0.95):
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        spike_grad = surrogate.atan()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.lif1 = snn.Leaky(beta=beta, spike_grad=spike_grad)

    def forward(self, x):
        mem1 = self.lif1.init_leaky()
        spk1_rec = []
        for step in range(x.shape[1]):
            cur1 = self.fc1(x[:, step, :])
            spk1, mem1 = self.lif1(cur1, mem1)
            spk1_rec.append(spk1)
        embedding = torch.stack(spk1_rec, dim=1).sum(dim=1)
        return embedding


# --- 3. 原型网络训练与评估逻辑 (已修正) ---
def run_prototypical_task(model, data, labels, n_way, k_shot, is_train=True):
    class_indices = defaultdict(list)
    # **核心修正**: 始终使用 .item() 将tensor标签转为python整数作为字典键
    for i, label in enumerate(labels):
        class_indices[label.item()].append(i)

    unique_labels = list(class_indices.keys())

    task_classes = random.sample(unique_labels, n_way)

    support_indices, query_indices = [], []
    for class_id in task_classes:
        sampled_indices = np.random.choice(class_indices[class_id], k_shot, replace=True)
        support_indices.extend(sampled_indices)
        query_indices.extend(sampled_indices)

    X_support = data[support_indices]
    y_support = labels[support_indices]
    X_query = data[query_indices]
    y_query = labels[query_indices]

    label_mapper = {global_label: local_label for local_label, global_label in enumerate(task_classes)}
    y_support_local = torch.tensor([label_mapper[l.item()] for l in y_support], device=data.device)
    y_query_local = torch.tensor([label_mapper[l.item()] for l in y_query], device=data.device)

    if is_train:
        model.train()
    else:
        model.eval()

    support_embeddings = model(X_support)

    prototypes = []
    for i in range(n_way):
        class_mask = (y_support_local == i)
        prototype = support_embeddings[class_mask].mean(dim=0)
        prototypes.append(prototype)
    prototypes = torch.stack(prototypes)

    query_embeddings = model(X_query)
    dists = -torch.cdist(query_embeddings, prototypes)
    loss = F.cross_entropy(dists, y_query_local)

    _, predicted = torch.max(dists, 1)
    acc = (predicted == y_query_local).float().mean().item() * 100

    return loss, acc


class PrototypicalSystem:
    """
    一个封装了SNN编码器和原型库的诊断系统。
    这个系统可以增量学习新的故障类型，而无需重新训练编码器。
    """

    def __init__(self, encoder):
        self.encoder = encoder
        self.encoder.eval()  # 始终保持评估模式，冻结权重
        self.prototypes = {}  # 字典: {label_idx: prototype_tensor}
        self.label_map = {}  # 字典: {label_idx: 'fault_name'}

    def learn_new_fault(self, fault_data, fault_name, device='cpu'):
        """
        从少量样本中学习一个新的故障类型。
        """
        print(f"\n学习新故障: '{fault_name}'...")
        # 确定新故障的唯一ID
        new_label_idx = len(self.prototypes)
        self.label_map[new_label_idx] = fault_name

        # 将数据转换为tensor
        fault_data_tensor = torch.tensor(fault_data, dtype=torch.float32).to(device)

        # 使用冻结的编码器计算新故障样本的特征向量
        with torch.no_grad():
            embeddings = self.encoder(fault_data_tensor)

        # 计算平均特征向量作为新原型
        new_prototype = embeddings.mean(dim=0)

        # 将新原型添加到库中
        self.prototypes[new_label_idx] = new_prototype
        print(f"'{fault_name}' 的原型已生成并添加到库中。")
        print(f"系统现在已学习 {len(self.prototypes)} 种故障。")

    def predict(self, query_data, device='cpu'):
        """
        对新的未知数据进行故障诊断。
        """
        if not self.prototypes:
            raise RuntimeError("系统尚未学习任何故障，无法进行预测。")

        query_tensor = torch.tensor(query_data, dtype=torch.float32).to(device)

        # 1. 提取查询数据的特征
        with torch.no_grad():
            query_embedding = self.encoder(query_tensor)

        # 2. 准备原型矩阵
        # 注意：要保证prototypes和label_map的顺序一致
        proto_indices = sorted(self.prototypes.keys())
        proto_tensors = torch.stack([self.prototypes[i] for i in proto_indices])

        # 3. 计算到所有原型的距离
        dists = -torch.cdist(query_embedding, proto_tensors)  # [num_queries, num_prototypes]

        # 4. 找到最近的原型作为预测结果
        pred_indices_local = torch.max(dists, 1).indices

        # 5. 将本地索引映射回全局标签和名称
        pred_indices_global = [proto_indices[i] for i in pred_indices_local]
        pred_names = [self.label_map[i] for i in pred_indices_global]

        return pred_names, pred_indices_global

    def evaluate_accuracy(self, test_data, test_labels, device='cpu'):
        """
        在给定的测试集上评估当前系统的准确率。
        """
        _, pred_indices = self.predict(test_data, device)
        correct = np.sum(np.array(pred_indices) == test_labels)
        accuracy = 100 * correct / len(test_labels)
        return accuracy

# --- 4. 主程序 (无变动) ---
if __name__ == '__main__':
    DATA_DIR = r'/EHA_data/sin信号/csv数据_20mm02hz全周期故障/早期故障'
    WINDOW_SIZE, STEP_SIZE = 1000, 1000
    HIDDEN_SIZE = 128
    BETA = 0.95
    LEARNING_RATE = 1e-3

    NUM_TASKS = 200
    N_WAY = 5   # 学几种例句
    K_SHOT = 4  # 分辨的时候参考几个例句
    EVAL_TASKS = 100
    EVAL_INTERVAL = 100

    X, y, label_map = load_and_window_data(DATA_DIR, WINDOW_SIZE, STEP_SIZE)
    print(f"数据加载完成。总窗口数: {X.shape[0]}, 标签映射: {label_map}")

    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train_tensor = torch.tensor(y_train, dtype=torch.long).to(device)
    X_test_tensor = torch.tensor(X_test, dtype=torch.float32).to(device)
    y_test_tensor = torch.tensor(y_test, dtype=torch.long).to(device)

    model = SNNEncoder(input_size=X.shape[2], hidden_size=HIDDEN_SIZE, beta=BETA).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)

    print(f"\n开始原型网络训练 ({N_WAY}-way, {K_SHOT}-shot)...")

    for task_num in range(NUM_TASKS):
        loss, acc = run_prototypical_task(model, X_train_tensor, y_train_tensor, N_WAY, K_SHOT, is_train=True)

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        if (task_num + 1) % EVAL_INTERVAL == 0:
            model.eval()
            with torch.no_grad():
                test_losses, test_accs = [], []
                for _ in range(EVAL_TASKS):
                    test_loss, test_acc = run_prototypical_task(model, X_test_tensor, y_test_tensor, N_WAY, K_SHOT,
                                                                is_train=False)
                    test_losses.append(test_loss.item())
                    test_accs.append(test_acc)

            avg_test_loss = np.mean(test_losses)
            avg_test_acc = np.mean(test_accs)

            print(f"任务 [{task_num + 1}/{NUM_TASKS}] | "
                  f"当前训练损失: {loss.item():.4f}, 训练准确率: {acc:.2f}% | "
                  f"平均测试损失: {avg_test_loss:.4f}, 平均测试准确率: {avg_test_acc:.2f}%")

    print("\n训练完成。")



    # ===================================================================
    #                  部署与持续学习/泛化测试阶段
    # ===================================================================

    # --- 参数设置 ---
    # !! 请修改为您的新工况数据文件夹路径 !!
    NEW_CONDITION_DIR = r'/EHA_data/sin信号/csv数据_10mm01hz全周期数据/前期故障'
    # 从新工况中选择一个故障用于增量学习演示
    # !! 请修改为您想用于增量学习的文件名 (不含.csv) !!
    INCREMENTAL_FAULT_NAME = "堵塞80.5"

    # --- 场景一: 增量学习 (Continual Learning) ---
    print("\n" + "=" * 50)
    print("场景一: 增量学习 - 系统学习一个全新的故障类型")
    print("=" * 50)

    # 1. 初始化诊断系统，并用训练集为已知故障构建原型库
    diagnostic_system = PrototypicalSystem(model)
    print("用原始训练数据构建初始原型库...")
    unique_train_labels = np.unique(y_train)
    for label_idx in unique_train_labels:
        class_data = X_train[y_train == label_idx]
        fault_name = [name for name, idx in label_map.items() if idx == label_idx][0]
        diagnostic_system.learn_new_fault(class_data, fault_name, device)

    # 2. 在增量学习前，测试系统在原始8类故障上的性能
    base_accuracy = diagnostic_system.evaluate_accuracy(X_test, y_test, device)
    print(f"\n增量学习前，系统在原始 {len(unique_train_labels)} 类测试集上的准确率: {base_accuracy:.2f}%")

    # 3. 从新工况文件夹加载一个指定的新故障数据，并学习它
    try:
        incremental_fault_path = os.path.join(NEW_CONDITION_DIR, f"{INCREMENTAL_FAULT_NAME}.csv")
        # 使用原始的数据加载和窗口化逻辑来处理新文件
        inc_windows, _, _ = load_and_window_data(
            data_dir=NEW_CONDITION_DIR,  # 临时指向新目录
            window_size=WINDOW_SIZE,
            step=STEP_SIZE
        )
        # 筛选出我们想要的那个增量故障的数据
        # 注意: load_and_window_data会加载目录下所有文件, 我们需要从中筛选
        temp_label_map_inc = {os.path.basename(f).split('.csv')[0]: i for i, f in
                              enumerate(glob.glob(os.path.join(NEW_CONDITION_DIR, '*.csv')))}
        inc_label_idx = temp_label_map_inc[INCREMENTAL_FAULT_NAME]

        # 假设所有从load_and_window_data返回的数据都在一个数组里，我们需要找到对应的标签
        # 为了简化，我们重新加载一次，只加载这一个文件
        df_inc = pd.read_csv(incremental_fault_path)
        scaler = MinMaxScaler()
        data_scaled_inc = scaler.fit_transform(df_inc)
        new_fault_data = []
        for i in range(0, df_inc.shape[0] - WINDOW_SIZE + 1, STEP_SIZE):
            new_fault_data.append(data_scaled_inc[i:i + WINDOW_SIZE, :])
        new_fault_data = np.array(new_fault_data)

    except FileNotFoundError:
        print(f"错误: 增量学习故障文件未找到: {incremental_fault_path}")
        new_fault_data = None

    if new_fault_data is not None:
        NEW_FAULT_LABEL = len(label_map)  # 为新故障分配一个全局唯一ID
        diagnostic_system.learn_new_fault(new_fault_data, INCREMENTAL_FAULT_NAME, device)

        # 4. 创建一个包含新旧故障的混合测试集
        num_old_samples = len(new_fault_data)
        old_test_indices = np.random.choice(len(X_test), num_old_samples, replace=False)
        X_test_subset, y_test_subset = X_test[old_test_indices], y_test[old_test_indices]

        combined_test_data = np.concatenate([X_test_subset, new_fault_data])
        combined_test_labels = np.concatenate([y_test_subset, np.full(len(new_fault_data), NEW_FAULT_LABEL)])

        # 5. 在混合测试集上评估
        incremental_accuracy = diagnostic_system.evaluate_accuracy(combined_test_data, combined_test_labels, device)
        print(f"\n增量学习后，系统在包含新旧故障的混合测试集上的准确率: {incremental_accuracy:.2f}%")

    # --- 场景二: 零样本泛化到全新工况 (直接测试) ---
    print("\n" + "=" * 50)
    print(f"场景二: 零样本泛化 - 直接在新工况 '{NEW_CONDITION_DIR}' 上进行诊断")
    print("=" * 50)

    try:
        # 1. 加载全新工况的所有数据
        new_cond_windows, new_cond_labels, new_cond_label_map = load_and_window_data(
            data_dir=NEW_CONDITION_DIR,
            window_size=WINDOW_SIZE,
            step=STEP_SIZE
        )
        print("新工况数据加载完成。标签映射:", new_cond_label_map)

        # 2. 模拟现场应用：为新工况的每种故障提供K个样本作为"参考"（Support Set）
        zero_shot_system = PrototypicalSystem(model)

        X_support, y_support = [], []
        X_query, y_query = [], []
        K_SHOT_FOR_NEW_COND = 5  # 假设现场工程师为每个新故障采集了5个样本

        # 对新工况的每个类别进行划分
        for label_idx, fault_name in new_cond_label_map.items():
            class_data = new_cond_windows[new_cond_labels == label_idx]
            if len(class_data) < K_SHOT_FOR_NEW_COND:
                print(
                    f"警告: 故障 '{fault_name}' 的样本数 ({len(class_data)}) 少于 K_SHOT ({K_SHOT_FOR_NEW_COND})。将使用所有可用样本作为Support Set，Query Set为空。")
                support_data = class_data
                query_data = np.array([])
            else:
                support_data = class_data[:K_SHOT_FOR_NEW_COND]
                query_data = class_data[K_SHOT_FOR_NEW_COND:]

            # 用 support set 让新系统“即时学习”
            zero_shot_system.learn_new_fault(support_data, fault_name, device)

            # 剩下的 query_data 用于最终测试
            if query_data.shape[0] > 0:
                X_query.append(query_data)
                y_query.append(np.full(len(query_data), label_idx))

        if not X_query:
            print("错误: 新工况数据不足，无法构建有效的Query Set进行评估。")
        else:
            # 合并测试数据 (Query Set)
            X_query = np.concatenate(X_query)
            y_query = np.concatenate(y_query)

            # 3. 在完全没见过的工况数据上，直接进行测试
            zero_shot_accuracy = zero_shot_system.evaluate_accuracy(X_query, y_query, device)
            print(
                f"\n直接泛化测试：系统在新工况上的 {len(new_cond_label_map)}-way, {K_SHOT_FOR_NEW_COND}-shot 诊断准确率: {zero_shot_accuracy:.2f}%")

    except FileNotFoundError:
        print(f"错误: 新工况数据文件夹未找到或为空: '{NEW_CONDITION_DIR}'")
    except Exception as e:
        print(f"处理新工况数据时发生错误: {e}")