# # deploy_and_test.py
#
# #%%
# import torch
# import torch.nn as nn
# from sklearn.model_selection import train_test_split
# import snntorch as snn
# from snntorch import surrogate
# import pandas as pd
# import numpy as np
# import os
# import glob
# from sklearn.preprocessing import MinMaxScaler
#
#
# # (load_and_window_data 和 SNNEncoder 定义保持不变)
# def load_and_window_data(data_dir, window_size, step):
#     all_files = glob.glob(os.path.join(data_dir, '*.csv'))
#     all_windows, all_labels = [], []
#     # 按照文件名排序，确保每次加载的标签映射顺序一致
#     all_files.sort()
#     label_map = {os.path.basename(file).split('.csv')[0]: i for i, file in enumerate(all_files)}
#     for label_name, label_idx in label_map.items():
#         file_path = os.path.join(data_dir, f"{label_name}.csv")
#         df = pd.read_csv(file_path)
#         # 检查文件行数是否足够
#         if df.shape[0] < window_size:
#             print(f"警告: 文件 '{file_path}' 的行数 ({df.shape[0]}) 少于 WINDOW_SIZE ({window_size})，将被跳过。")
#             continue
#         scaler = MinMaxScaler()
#         data_scaled = scaler.fit_transform(df)
#         for i in range(0, df.shape[0] - window_size + 1, step):
#             all_windows.append(data_scaled[i:i + window_size, :])
#             all_labels.append(label_idx)
#     return np.array(all_windows), np.array(all_labels), label_map
#
#
# class SNNEncoder(nn.Module):
#     def __init__(self, input_size, hidden_size, beta=0.95):
#         super().__init__()
#         self.input_size = input_size
#         self.hidden_size = hidden_size
#         spike_grad = surrogate.atan()
#         self.fc1 = nn.Linear(input_size, hidden_size)
#         self.lif1 = snn.Leaky(beta=beta, spike_grad=spike_grad)
#
#     def forward(self, x):
#         mem1 = self.lif1.init_leaky()
#         spk1_rec = []
#         for step in range(x.shape[1]):
#             cur1 = self.fc1(x[:, step, :])
#             spk1, mem1 = self.lif1(cur1, mem1)
#             spk1_rec.append(spk1)
#         embedding = torch.stack(spk1_rec, dim=1).sum(dim=1)
#         return embedding
#
#
# class PrototypicalSystem:
#     def __init__(self, encoder):
#         self.encoder = encoder
#         self.encoder.eval()
#         self.prototypes = {}
#         self.label_map_int_to_name = {}  # {0: 'fault_name_A', 1: 'fault_name_B'}
#         self.label_map_name_to_int = {}  # {'fault_name_A': 0, 'fault_name_B': 1}
#
#     def learn_new_fault(self, fault_data, fault_name, device='cpu'):
#         print(f"\n学习故障: '{fault_name}'...")
#
#         # **修正点 1**: 逻辑更清晰，基于名字来决定是新增还是更新
#         if fault_name in self.label_map_name_to_int:
#             label_idx = self.label_map_name_to_int[fault_name]
#             print(f"'{fault_name}' 已存在 (ID: {label_idx})，将更新其原型。")
#         else:
#             label_idx = len(self.prototypes)
#             self.label_map_name_to_int[fault_name] = label_idx
#             self.label_map_int_to_name[label_idx] = fault_name
#             print(f"'{fault_name}' 是新故障，分配新ID: {label_idx}。")
#
#         # 检查是否有足够的样本来学习
#         if fault_data.shape[0] == 0:
#             print(f"警告: 无法学习 '{fault_name}'，因为没有提供任何数据样本。")
#             return
#
#         fault_data_tensor = torch.tensor(fault_data, dtype=torch.float32).to(device)
#         with torch.no_grad():
#             embeddings = self.encoder(fault_data_tensor)
#
#         new_prototype = embeddings.mean(dim=0)
#         self.prototypes[label_idx] = new_prototype
#         print(f"'{fault_name}' 的原型已生成。系统现在已学习 {len(self.prototypes)} 种故障。")
#
#     def predict(self, query_data, device='cpu'):
#         # ... (此函数无需修改)
#         if not self.prototypes:
#             raise RuntimeError("系统尚未学习任何故障，无法进行预测。")
#         query_tensor = torch.tensor(query_data, dtype=torch.float32).to(device)
#         if query_tensor.ndim == 2:
#             query_tensor = query_tensor.unsqueeze(0)
#         with torch.no_grad():
#             query_embedding = self.encoder(query_tensor)
#         proto_indices = sorted(self.prototypes.keys())
#         proto_tensors = torch.stack([self.prototypes[i] for i in proto_indices]).to(device)
#         dists = -torch.cdist(query_embedding, proto_tensors)
#         pred_indices_local = torch.max(dists, 1).indices
#         pred_indices_global = [proto_indices[i] for i in pred_indices_local]
#         pred_names = [self.label_map_int_to_name[i] for i in pred_indices_global]
#         return pred_names, pred_indices_global
#
#     def evaluate_accuracy(self, test_data, test_labels, device='cpu'):
#         # ... (此函数无需修改)
#         _, pred_indices = self.predict(test_data, device)
#         correct = np.sum(np.array(pred_indices) == test_labels)
#         accuracy = 100 * correct / len(test_labels)
#         return accuracy
#
#
# # --- 主部署与测试程序 ---
# if __name__ == '__main__':
#     MODEL_PATH = 'snn_encoder.pth'
#     ORIGINAL_DATA_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\早期故障'
#     NEW_CONDITION_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_10mm01hz全周期数据\前期故障'
#
#     # **修正点 2**: 为增量学习的故障指定一个独一无二的名字
#     INCREMENTAL_FAULT_NAME_FROM_NEW_COND = "堵塞80.5"  # 这是在新工况文件夹里的文件名
#     INCREMENTAL_FAULT_LEARNED_NAME = "工况B_堵塞80.5"  # 这是我们赋予它的新名字
#
#     HIDDEN_SIZE = 128
#     WINDOW_SIZE = 1024
#     STEP_SIZE = 1024
#
#     device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
#     print(f"使用设备: {device}")
#
#     # 加载模型
#     X_orig_for_shape, _, _ = load_and_window_data(ORIGINAL_DATA_DIR, WINDOW_SIZE, STEP_SIZE)
#     if X_orig_for_shape.shape[0] == 0:
#         raise ValueError(f"无法从 '{ORIGINAL_DATA_DIR}' 加载任何数据，请检查文件和WINDOW_SIZE。")
#     INPUT_SIZE = X_orig_for_shape.shape[2]
#
#     loaded_encoder = SNNEncoder(input_size=INPUT_SIZE, hidden_size=HIDDEN_SIZE)
#     loaded_encoder.load_state_dict(torch.load(MODEL_PATH, map_location=device))
#     loaded_encoder.to(device)
#     loaded_encoder.eval()
#     print(f"模型 '{MODEL_PATH}' 加载成功。")
#
#     # --- 场景一: 增量学习 ---
#     print("\n" + "=" * 50)
#     print("场景一: 增量学习")
#     print("=" * 50)
#
#     diagnostic_system = PrototypicalSystem(loaded_encoder)
#
#     X_orig, y_orig, label_map_orig = load_and_window_data(ORIGINAL_DATA_DIR, WINDOW_SIZE, STEP_SIZE)
#     X_train_orig, X_test_orig, y_train_orig, y_test_orig = train_test_split(X_orig, y_orig, test_size=0.2,
#                                                                             random_state=42, stratify=y_orig)
#
#     print("用原始训练数据构建初始原型库...")
#     for fault_name, label_idx in label_map_orig.items():
#         class_data = X_train_orig[y_train_orig == label_idx]
#         diagnostic_system.learn_new_fault(class_data, fault_name, device)
#
#     base_accuracy = diagnostic_system.evaluate_accuracy(X_test_orig, y_test_orig, device)
#     print(f"\n增量学习前，系统在原始 {len(label_map_orig)} 类测试集上的准确率: {base_accuracy:.2f}%")
#
#     # 加载并学习新故障
#     try:
#         inc_fault_path = os.path.join(NEW_CONDITION_DIR, f"{INCREMENTAL_FAULT_NAME_FROM_NEW_COND}.csv")
#         df_inc = pd.read_csv(inc_fault_path)
#         if df_inc.shape[0] < WINDOW_SIZE:
#             raise ValueError(f"文件 '{inc_fault_path}' 行数不足 ({df_inc.shape[0]})")
#         scaler = MinMaxScaler()
#         data_scaled_inc = scaler.fit_transform(df_inc)
#         new_fault_data = []
#         for i in range(0, df_inc.shape[0] - WINDOW_SIZE + 1, STEP_SIZE):
#             new_fault_data.append(data_scaled_inc[i:i + WINDOW_SIZE, :])
#         new_fault_data = np.array(new_fault_data)
#
#         # **修正点 3**: 用新名字学习新故障，并获取其新ID
#         diagnostic_system.learn_new_fault(new_fault_data, INCREMENTAL_FAULT_LEARNED_NAME, device)
#         NEW_FAULT_LABEL = diagnostic_system.label_map_name_to_int[INCREMENTAL_FAULT_LEARNED_NAME]
#
#         # 创建混合测试集
#         num_old_samples = min(len(new_fault_data), len(X_test_orig))
#         old_test_indices = np.random.choice(len(X_test_orig), num_old_samples, replace=False)
#         X_test_subset, y_test_subset = X_test_orig[old_test_indices], y_test_orig[old_test_indices]
#
#         combined_test_data = np.concatenate([X_test_subset, new_fault_data])
#         combined_test_labels = np.concatenate([y_test_subset, np.full(len(new_fault_data), NEW_FAULT_LABEL)])
#
#         incremental_accuracy = diagnostic_system.evaluate_accuracy(combined_test_data, combined_test_labels, device)
#         print(f"\n增量学习后，在混合测试集上的准确率: {incremental_accuracy:.2f}%")
#     except (FileNotFoundError, ValueError) as e:
#         print(f"错误: 无法加载或处理增量学习故障文件 - {e}")
#
#     # --- 场景二: 零样本泛化到全新工况 ---
#     print("\n" + "=" * 50)
#     print(f"场景二: 零样本泛化 - '{NEW_CONDITION_DIR}'")
#     print("=" * 50)
#
#     try:
#         new_cond_windows, new_cond_labels, new_cond_label_map = load_and_window_data(NEW_CONDITION_DIR, WINDOW_SIZE,
#                                                                                      STEP_SIZE)
#         if new_cond_windows.shape[0] == 0:
#             raise ValueError("从新工况目录中未能加载任何有效数据窗口。请检查文件行数和WINDOW_SIZE。")
#         print("新工况数据加载完成。标签映射:", new_cond_label_map)
#
#         zero_shot_system = PrototypicalSystem(loaded_encoder)
#         X_query, y_query = [], []
#         K_SHOT_FOR_NEW_COND = 5
#
#         # **修正点 4**: 循环变量名正确
#         for fault_name, label_idx in new_cond_label_map.items():
#             class_data = new_cond_windows[new_cond_labels == label_idx]
#             if len(class_data) < K_SHOT_FOR_NEW_COND:
#                 print(f"警告: 故障 '{fault_name}' 的样本数 ({len(class_data)}) 少于 K_SHOT ({K_SHOT_FOR_NEW_COND})。")
#                 support_data = class_data
#                 query_data = np.array([])
#             else:
#                 support_data = class_data[:K_SHOT_FOR_NEW_COND]
#                 query_data = class_data[K_SHOT_FOR_NEW_COND:]
#
#             # 使用字符串名字来学习
#             zero_shot_system.learn_new_fault(support_data, fault_name, device)
#
#             if query_data.shape[0] > 0:
#                 X_query.append(query_data)
#                 # 使用整数索引作为真实标签
#                 y_query.append(np.full(len(query_data), label_idx))
#
#
#         if not X_query:
#             print("错误: 新工况数据不足，无法构建有效的Query Set进行评估。")
#         else:
#             X_query = np.concatenate(X_query)
#             y_query = np.concatenate(y_query)
#
#             zero_shot_accuracy = zero_shot_system.evaluate_accuracy(X_query, y_query, device)
#             print(f"\n直接泛化测试：系统在新工况上的诊断准确率: {zero_shot_accuracy:.2f}%")
#
#     except (FileNotFoundError, ValueError) as e:
#         print(f"处理新工况数据时发生错误: {e}")


import torch
import torch.nn as nn
from sklearn.model_selection import train_test_split
import snntorch as snn
from snntorch import surrogate
import pandas as pd
import numpy as np
import os
import glob
from sklearn.preprocessing import MinMaxScaler

# ----------- 全局 scaler：保证所有数据用同一归一化 -----------
GLOBAL_SCALER = None

def load_and_window_data(data_dir, window_size, step, scaler=None):
    """
    如果 scaler 为 None，内部 fit 一个并返回；
    否则直接用传入的 scaler 做 transform。
    返回值最后一个元素一定是 scaler。
    """
    global GLOBAL_SCALER
    all_files = sorted(glob.glob(os.path.join(data_dir, '*.csv')))
    if not all_files:
        raise ValueError(f'目录 {data_dir} 里没有 csv 文件')

    all_windows, all_labels = [], []
    label_map = {os.path.basename(f)[:-4]: idx for idx, f in enumerate(all_files)}

    # 第一次调用：内部 fit
    if scaler is None and GLOBAL_SCALER is None:
        scaler = MinMaxScaler()

    for label_name, label_idx in label_map.items():
        file_path = os.path.join(data_dir, f"{label_name}.csv")
        df = pd.read_csv(file_path)
        if len(df) < window_size:
            print(f'警告：{label_name} 行数不足，跳过')
            continue
        if scaler is None:          # 第一次 fit
            data_scaled = scaler.fit_transform(df)
        else:                       # 后续都用同一个
            data_scaled = scaler.transform(df)
        for i in range(0, len(df) - window_size + 1, step):
            all_windows.append(data_scaled[i:i + window_size, :])
            all_labels.append(label_idx)

    GLOBAL_SCALER = scaler        # 存到全局，后面直接拿
    return np.array(all_windows), np.array(all_labels), label_map, scaler


class SNNEncoder(nn.Module):
    def __init__(self, input_size, hidden_size, beta=0.95):
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        spike_grad = surrogate.atan()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.lif1 = snn.Leaky(beta=beta, spike_grad=spike_grad)

    def forward(self, x):
        mem1 = self.lif1.init_leaky()
        spk1_rec = []
        for step in range(x.shape[1]):
            cur1 = self.fc1(x[:, step, :])
            spk1, mem1 = self.lif1(cur1, mem1)
            spk1_rec.append(spk1)
        embedding = torch.stack(spk1_rec, dim=1).sum(dim=1)
        return embedding


class PrototypicalSystem:
    def __init__(self, encoder):
        self.encoder = encoder
        self.encoder.eval()
        self.prototypes = {}
        self.label_map_int_to_name = {}
        self.label_map_name_to_int = {}

    def learn_new_fault(self, fault_data, fault_name, device='cpu'):
        if fault_name in self.label_map_name_to_int:
            label_idx = self.label_map_name_to_int[fault_name]
            print(f"'{fault_name}' 已存在 (ID: {label_idx})，将更新其原型。")
        else:
            label_idx = len(self.prototypes)
            self.label_map_name_to_int[fault_name] = label_idx
            self.label_map_int_to_name[label_idx] = fault_name
            print(f"'{fault_name}' 是新故障，分配新ID: {label_idx}。")

        if fault_data.shape[0] == 0:
            print(f"警告: 无法学习 '{fault_name}'，因为没有提供任何数据样本。")
            return

        fault_data_tensor = torch.tensor(fault_data, dtype=torch.float32).to(device)
        with torch.no_grad():
            embeddings = self.encoder(fault_data_tensor)
        new_prototype = embeddings.mean(dim=0)
        self.prototypes[label_idx] = new_prototype

    def predict(self, query_data, device='cpu'):
        if not self.prototypes:
            raise RuntimeError("系统尚未学习任何故障，无法进行预测。")
        query_tensor = torch.tensor(query_data, dtype=torch.float32).to(device)
        if query_tensor.ndim == 2:
            query_tensor = query_tensor.unsqueeze(0)
        with torch.no_grad():
            query_embedding = self.encoder(query_tensor)
        proto_indices = sorted(self.prototypes.keys())
        proto_tensors = torch.stack([self.prototypes[i] for i in proto_indices]).to(device)
        dists = -torch.cdist(query_embedding, proto_tensors)
        pred_indices_local = torch.max(dists, 1).indices
        pred_indices_global = [proto_indices[i] for i in pred_indices_local]
        pred_names = [self.label_map_int_to_name[i] for i in pred_indices_global]
        return pred_names, pred_indices_global

    def evaluate_accuracy(self, test_data, test_labels, device='cpu'):
        _, pred_indices = self.predict(test_data, device)
        correct = np.sum(np.array(pred_indices) == test_labels)
        accuracy = 100 * correct / len(test_labels)
        return accuracy


# ------------------------ 主程序 ------------------------
if __name__ == '__main__':
    MODEL_PATH        = 'snn_encoder.pth'
    ORIGINAL_DATA_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\早期故障'
    NEW_CONDITION_DIR = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_10mm01hz全周期数据\前期故障'

    INCREMENTAL_FAULT_NAME_FROM_NEW_COND = "堵塞80.5"
    INCREMENTAL_FAULT_LEARNED_NAME       = "工况B_堵塞80.5"

    HIDDEN_SIZE = 128
    WINDOW_SIZE = 1024
    STEP_SIZE   = 1024

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 1. 第一次加载原始数据，得到 scaler
    # 第一次加载：fit 一个 scaler
    X_orig, y_orig, label_map_orig, scaler = load_and_window_data(
        ORIGINAL_DATA_DIR, WINDOW_SIZE, STEP_SIZE)
    GLOBAL_SCALER = scaler  # 保存到全局
    if X_orig.shape[0] == 0:
        raise ValueError('原始目录没有有效数据')
    INPUT_SIZE = X_orig.shape[2]

    loaded_encoder = SNNEncoder(input_size=INPUT_SIZE, hidden_size=HIDDEN_SIZE)
    loaded_encoder.load_state_dict(torch.load(MODEL_PATH, map_location=device))
    loaded_encoder.to(device).eval()
    print(f"模型 '{MODEL_PATH}' 加载成功。")

    # -------------- 场景一：增量学习 --------------
    print("\n" + "="*50 + "\n场景一：增量学习\n" + "="*50)
    diagnostic_system = PrototypicalSystem(loaded_encoder)

    X_train_orig, X_test_orig, y_train_orig, y_test_orig = train_test_split(
        X_orig, y_orig, test_size=0.2, random_state=42, stratify=y_orig)

    for fault_name, label_idx in label_map_orig.items():
        class_data = X_train_orig[y_train_orig == label_idx]
        diagnostic_system.learn_new_fault(class_data, fault_name, device)

    base_acc = diagnostic_system.evaluate_accuracy(X_test_orig, y_test_orig, device)
    print(f"增量学习前，原始测试集准确率: {base_acc:.2f}%")

    # 加载新故障，留一半做训练、一半做测试
    inc_df = pd.read_csv(os.path.join(NEW_CONDITION_DIR, f"{INCREMENTAL_FAULT_NAME_FROM_NEW_COND}.csv"))
    if len(inc_df) < WINDOW_SIZE:
        raise ValueError('新故障文件行数不足')
    inc_scaled = GLOBAL_SCALER.transform(inc_df)
    new_fault_data = np.stack([inc_scaled[i:i+WINDOW_SIZE, :]
                               for i in range(0, len(inc_df)-WINDOW_SIZE+1, STEP_SIZE)])
    X_new_train, X_new_test = train_test_split(
        new_fault_data, test_size=0.5, random_state=42)

    diagnostic_system.learn_new_fault(X_new_train, INCREMENTAL_FAULT_LEARNED_NAME, device)
    NEW_FAULT_LABEL = diagnostic_system.label_map_name_to_int[INCREMENTAL_FAULT_LEARNED_NAME]

    # 混合测试集
    num_old = min(len(X_new_test), len(X_test_orig))
    old_idx = np.random.choice(len(X_test_orig), num_old, replace=False)
    X_test_subset, y_test_subset = X_test_orig[old_idx], y_test_orig[old_idx]

    combined_X = np.concatenate([X_test_subset, X_new_test])
    combined_y = np.concatenate([y_test_subset,
                                 np.full(len(X_new_test), NEW_FAULT_LABEL)])
    inc_acc = diagnostic_system.evaluate_accuracy(combined_X, combined_y, device)
    print(f"增量学习后，混合测试集准确率: {inc_acc:.2f}%")

    # -------------- 场景二：零样本泛化 --------------
    print("\n" + "="*50 + "\n场景二：零样本泛化\n" + "="*50)
    new_cond_windows, new_cond_labels, new_cond_label_map, _ = load_and_window_data(
        NEW_CONDITION_DIR, WINDOW_SIZE, STEP_SIZE, scaler)

    zero_shot_system = PrototypicalSystem(loaded_encoder)
    X_query, y_query = [], []
    K_SHOT = 5

    for fault_name, _ in new_cond_label_map.items():
        class_data = new_cond_windows[new_cond_labels == new_cond_label_map[fault_name]]
        if len(class_data) <= K_SHOT:
            print(f'警告：{fault_name} 样本<=K_SHOT，跳过评估')
            continue
        support_data = class_data[:K_SHOT]
        query_data   = class_data[K_SHOT:]

        zero_shot_system.learn_new_fault(support_data, fault_name, device)
        system_label = zero_shot_system.label_map_name_to_int[fault_name]
        X_query.append(query_data)
        y_query.append(np.full(len(query_data), system_label))

    if X_query:
        X_query = np.concatenate(X_query)
        y_query = np.concatenate(y_query)
        zs_acc = zero_shot_system.evaluate_accuracy(X_query, y_query, device)
        print(f"新工况零样本准确率: {zs_acc:.2f}%")
    else:
        print("新工况数据不足，无法构建有效 Query Set。")