import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import snntorch as snn
from snntorch import surrogate

import pandas as pd
import numpy as np
import os
import glob
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
import random
from collections import defaultdict


# --- 1. 数据加载与预处理 (无变动) ---
def load_and_window_data(data_dir, window_size, step):
    all_files = glob.glob(os.path.join(data_dir, '*.csv'))
    all_windows, all_labels = [], []
    # 如果您的标签是从1开始的，请使用 enumerate(all_files, 1)
    label_map = {os.path.basename(file).split('.csv')[0]: i for i, file in enumerate(all_files)}
    for label_name, label_idx in label_map.items():
        file_path = os.path.join(data_dir, f"{label_name}.csv")
        df = pd.read_csv(file_path)
        scaler = MinMaxScaler()
        data_scaled = scaler.fit_transform(df)
        for i in range(0, df.shape[0] - window_size + 1, step):
            all_windows.append(data_scaled[i:i + window_size, :])
            all_labels.append(label_idx)
    return np.array(all_windows), np.array(all_labels), label_map


# --- 2. SNN 编码器模型 (无变动) ---
class SNNEncoder(nn.Module):
    def __init__(self, input_size, hidden_size, beta=0.95):
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        spike_grad = surrogate.atan()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.lif1 = snn.Leaky(beta=beta, spike_grad=spike_grad)

    def forward(self, x):
        mem1 = self.lif1.init_leaky()
        spk1_rec = []
        for step in range(x.shape[1]):
            cur1 = self.fc1(x[:, step, :])
            spk1, mem1 = self.lif1(cur1, mem1)
            spk1_rec.append(spk1)
        embedding = torch.stack(spk1_rec, dim=1).sum(dim=1)
        return embedding


# --- 3. 原型网络训练与评估逻辑 (已修正) ---
def run_prototypical_task(model, data, labels, n_way, k_shot, is_train=True):
    class_indices = defaultdict(list)
    # **核心修正**: 始终使用 .item() 将tensor标签转为python整数作为字典键
    for i, label in enumerate(labels):
        class_indices[label.item()].append(i)

    unique_labels = list(class_indices.keys())

    task_classes = random.sample(unique_labels, n_way)

    support_indices, query_indices = [], []
    for class_id in task_classes:
        sampled_indices = np.random.choice(class_indices[class_id], k_shot, replace=True)
        support_indices.extend(sampled_indices)
        query_indices.extend(sampled_indices)

    X_support = data[support_indices]
    y_support = labels[support_indices]
    X_query = data[query_indices]
    y_query = labels[query_indices]

    label_mapper = {global_label: local_label for local_label, global_label in enumerate(task_classes)}
    y_support_local = torch.tensor([label_mapper[l.item()] for l in y_support], device=data.device)
    y_query_local = torch.tensor([label_mapper[l.item()] for l in y_query], device=data.device)

    if is_train:
        model.train()
    else:
        model.eval()

    support_embeddings = model(X_support)

    prototypes = []
    for i in range(n_way):
        class_mask = (y_support_local == i)
        prototype = support_embeddings[class_mask].mean(dim=0)
        prototypes.append(prototype)
    prototypes = torch.stack(prototypes)

    query_embeddings = model(X_query)
    dists = -torch.cdist(query_embeddings, prototypes)
    loss = F.cross_entropy(dists, y_query_local)

    _, predicted = torch.max(dists, 1)
    acc = (predicted == y_query_local).float().mean().item() * 100

    return loss, acc


# --- 4. 主程序 (无变动) ---
if __name__ == '__main__':
    DATA_DIR = r'/EHA_data/sin信号/csv数据_20mm02hz全周期故障/早期故障'
    WINDOW_SIZE, STEP_SIZE = 1000, 1000
    HIDDEN_SIZE = 128
    BETA = 0.95
    LEARNING_RATE = 1e-3

    NUM_TASKS = 200
    N_WAY = 5   # 学几种例句
    K_SHOT = 4  # 分辨的时候参考几个例句
    EVAL_TASKS = 100
    EVAL_INTERVAL = 100

    X, y, label_map = load_and_window_data(DATA_DIR, WINDOW_SIZE, STEP_SIZE)
    print(f"数据加载完成。总窗口数: {X.shape[0]}, 标签映射: {label_map}")

    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    X_train_tensor = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train_tensor = torch.tensor(y_train, dtype=torch.long).to(device)
    X_test_tensor = torch.tensor(X_test, dtype=torch.float32).to(device)
    y_test_tensor = torch.tensor(y_test, dtype=torch.long).to(device)

    model = SNNEncoder(input_size=X.shape[2], hidden_size=HIDDEN_SIZE, beta=BETA).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)

    print(f"\n开始原型网络训练 ({N_WAY}-way, {K_SHOT}-shot)...")

    for task_num in range(NUM_TASKS):
        loss, acc = run_prototypical_task(model, X_train_tensor, y_train_tensor, N_WAY, K_SHOT, is_train=True)

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        if (task_num + 1) % EVAL_INTERVAL == 0:
            model.eval()
            with torch.no_grad():
                test_losses, test_accs = [], []
                for _ in range(EVAL_TASKS):
                    test_loss, test_acc = run_prototypical_task(model, X_test_tensor, y_test_tensor, N_WAY, K_SHOT,
                                                                is_train=False)
                    test_losses.append(test_loss.item())
                    test_accs.append(test_acc)

            avg_test_loss = np.mean(test_losses)
            avg_test_acc = np.mean(test_accs)

            print(f"任务 [{task_num + 1}/{NUM_TASKS}] | "
                  f"当前训练损失: {loss.item():.4f}, 训练准确率: {acc:.2f}% | "
                  f"平均测试损失: {avg_test_loss:.4f}, 平均测试准确率: {avg_test_acc:.2f}%")

    print("\n训练完成。")


