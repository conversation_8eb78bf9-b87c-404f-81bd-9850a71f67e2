# SNN Meta-Learning for Sensor Robustness
# 基于元学习的传感器鲁棒性SNN故障诊断

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import snntorch as snn
from snntorch import surrogate
from snntorch import utils

import pandas as pd
import numpy as np
import os
import glob
import random
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from tqdm import tqdm
import copy


# ==========================================================
#              1. 传感器故障模拟器
# ==========================================================
class SensorFaultSimulator:
    """模拟各种传感器故障情况"""
    
    def __init__(self, num_sensors):
        self.num_sensors = num_sensors
        
    def generate_fault_config(self):
        """生成一个随机的传感器故障配置"""
        fault_config = {
            'missing_sensors': [],      # 缺失的传感器索引
            'offset_sensors': {},       # 偏移的传感器 {sensor_idx: offset_value}
            'noise_sensors': {},        # 噪声增大的传感器 {sensor_idx: noise_scale}
            'drift_sensors': {},        # 漂移的传感器 {sensor_idx: drift_rate}
        }
        
        # 随机选择故障类型和数量
        num_faults = random.randint(0, min(3, self.num_sensors // 2))
        
        for _ in range(num_faults):
            fault_type = random.choice(['missing', 'offset', 'noise', 'drift'])
            sensor_idx = random.randint(0, self.num_sensors - 1)
            
            if fault_type == 'missing' and sensor_idx not in fault_config['missing_sensors']:
                fault_config['missing_sensors'].append(sensor_idx)
            elif fault_type == 'offset':
                fault_config['offset_sensors'][sensor_idx] = random.uniform(-0.5, 0.5)
            elif fault_type == 'noise':
                fault_config['noise_sensors'][sensor_idx] = random.uniform(1.5, 3.0)
            elif fault_type == 'drift':
                fault_config['drift_sensors'][sensor_idx] = random.uniform(-0.01, 0.01)
                
        return fault_config
    
    def apply_faults(self, data, fault_config):
        """将故障应用到数据上
        Args:
            data: [batch_size, time_steps, num_sensors]
            fault_config: 故障配置字典
        Returns:
            faulty_data: 应用故障后的数据
        """
        faulty_data = data.clone()
        batch_size, time_steps, num_sensors = data.shape
        
        # 应用缺失传感器（置零）
        for sensor_idx in fault_config['missing_sensors']:
            faulty_data[:, :, sensor_idx] = 0
            
        # 应用偏移
        for sensor_idx, offset in fault_config['offset_sensors'].items():
            faulty_data[:, :, sensor_idx] += offset
            
        # 应用噪声
        for sensor_idx, noise_scale in fault_config['noise_sensors'].items():
            noise = torch.randn_like(faulty_data[:, :, sensor_idx]) * noise_scale * 0.1
            faulty_data[:, :, sensor_idx] += noise
            
        # 应用漂移
        for sensor_idx, drift_rate in fault_config['drift_sensors'].items():
            drift = torch.arange(time_steps, dtype=torch.float32).unsqueeze(0) * drift_rate
            drift = drift.expand(batch_size, -1)
            faulty_data[:, :, sensor_idx] += drift.to(faulty_data.device)
            
        return faulty_data


# ==========================================================
#              2. 元学习数据集
# ==========================================================
class MetaFaultDataset(Dataset):
    """元学习故障数据集"""
    
    def __init__(self, data, labels, num_sensors, support_shots=5, query_shots=10):
        self.data = torch.tensor(data, dtype=torch.float32)
        self.labels = torch.tensor(labels, dtype=torch.long)
        self.num_sensors = num_sensors
        self.support_shots = support_shots
        self.query_shots = query_shots
        self.fault_simulator = SensorFaultSimulator(num_sensors)
        
        # 按类别组织数据
        self.class_data = {}
        for i in range(len(self.data)):
            label = self.labels[i].item()
            if label not in self.class_data:
                self.class_data[label] = []
            self.class_data[label].append(i)
    
    def __len__(self):
        return 1000  # 元训练episode数量
    
    def __getitem__(self, idx):
        """生成一个元学习episode"""
        # 生成故障配置
        fault_config = self.fault_simulator.generate_fault_config()
        
        # 选择类别
        num_classes = len(self.class_data)
        selected_classes = random.sample(list(self.class_data.keys()), 
                                       min(num_classes, random.randint(2, min(8, num_classes))))
        
        support_data, support_labels = [], []
        query_data, query_labels = [], []
        
        for class_idx, original_label in enumerate(selected_classes):
            # 为每个类别选择support和query样本
            class_samples = self.class_data[original_label]
            selected_samples = random.sample(class_samples, 
                                           min(len(class_samples), self.support_shots + self.query_shots))
            
            support_indices = selected_samples[:self.support_shots]
            query_indices = selected_samples[self.support_shots:self.support_shots + self.query_shots]
            
            # Support set
            for idx in support_indices:
                data_sample = self.data[idx]
                faulty_data = self.fault_simulator.apply_faults(data_sample.unsqueeze(0), fault_config)
                support_data.append(faulty_data.squeeze(0))
                support_labels.append(class_idx)  # 重新映射标签
            
            # Query set
            for idx in query_indices:
                data_sample = self.data[idx]
                faulty_data = self.fault_simulator.apply_faults(data_sample.unsqueeze(0), fault_config)
                query_data.append(faulty_data.squeeze(0))
                query_labels.append(class_idx)  # 重新映射标签
        
        return {
            'support_data': torch.stack(support_data),
            'support_labels': torch.tensor(support_labels, dtype=torch.long),
            'query_data': torch.stack(query_data),
            'query_labels': torch.tensor(query_labels, dtype=torch.long),
            'fault_config': fault_config,
            'num_classes': len(selected_classes)
        }


# ==========================================================
#              3. Izhikevich神经元（复用之前的实现）
# ==========================================================
class IzhikevichNeuron(nn.Module):
    def __init__(self, a=0.02, b=0.2, c=-65, d=8, threshold=30.0, spike_grad=None):
        super().__init__()
        self.a = a
        self.b = b
        self.c = c
        self.d = d
        self.threshold = threshold
        
        if spike_grad is None:
            self.spike_grad = surrogate.atan()
        else:
            self.spike_grad = spike_grad

    def init_izh(self):
        return torch.zeros(1), torch.zeros(1)

    def forward(self, x, v=None, u=None):
        if v is None or u is None or v.device != x.device:
            v = torch.full_like(x, self.c)
            u = torch.zeros_like(x)
        
        dv = 0.04 * v ** 2 + 5 * v + 140 - u + x
        du = self.a * (self.b * v - u)

        v = v + dv
        u = u + du

        spk = self.spike_grad(v - self.threshold)

        v = torch.where(spk > 0, torch.full_like(v, self.c), v)
        u = torch.where(spk > 0, u + self.d, u)

        return spk, v, u


# ==========================================================
#              4. 多神经元层（复用之前的实现）
# ==========================================================
class MultiNeuronLayer(nn.Module):
    def __init__(self, input_size, hidden_size, beta_lif, beta_alpha, alpha_val):
        super().__init__()
        spike_grad = surrogate.atan()

        self.fc_lif = nn.Linear(input_size, hidden_size)
        self.lif = snn.Leaky(beta=beta_lif, spike_grad=spike_grad)

        self.fc_izh = nn.Linear(input_size, hidden_size)
        self.izh = IzhikevichNeuron(spike_grad=spike_grad)

        self.fc_alpha = nn.Linear(input_size, hidden_size)
        self.alpha = snn.Alpha(beta=beta_alpha, alpha=alpha_val, spike_grad=spike_grad)

        self.fusion_layer = nn.Linear(hidden_size * 3, hidden_size)
        self.soma_lif = snn.Leaky(beta=beta_lif, spike_grad=spike_grad)

    def forward(self, x, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma):
        spk_lif, mem_lif = self.lif(self.fc_lif(x), mem_lif)
        spk_izh, v_izh, u_izh = self.izh(self.fc_izh(x), v_izh, u_izh)
        spk_alpha, syn_exc_alpha, syn_inh_alpha, mem_alpha = self.alpha(
            self.fc_alpha(x), syn_exc_alpha, syn_inh_alpha, mem_alpha)

        fused_spikes = torch.cat([spk_lif, spk_izh, spk_alpha], dim=1)
        soma_current = self.fusion_layer(fused_spikes)
        output_spikes, mem_soma = self.soma_lif(soma_current, mem_soma)

        return output_spikes, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma


# ==========================================================
#              5. 元学习SNN分类器
# ==========================================================
class MetaSNNClassifier(nn.Module):
    def __init__(self, input_size, hidden_size, max_num_classes=8, num_layers=2, 
                 dropout_prob=0.3, beta_lif=0.95, beta_alpha=0.8, alpha_val=0.9):
        super().__init__()
        self.layers = nn.ModuleList()
        self.layers.append(MultiNeuronLayer(input_size, hidden_size, beta_lif, beta_alpha, alpha_val))
        for _ in range(num_layers - 1):
            self.layers.append(MultiNeuronLayer(hidden_size, hidden_size, beta_lif, beta_alpha, alpha_val))
        
        self.dropout = nn.Dropout(dropout_prob)
        self.fc_out = nn.Linear(hidden_size, max_num_classes)  # 支持最大类别数
        
    def forward(self, x, num_classes=None):
        # 初始化所有层的状态
        layer_states = []
        for layer in self.layers:
            mem_lif = layer.lif.init_leaky()
            v_izh, u_izh = layer.izh.init_izh()
            syn_exc_alpha, syn_inh_alpha, mem_alpha = layer.alpha.init_alpha()
            mem_soma = layer.soma_lif.init_leaky()
            layer_states.append((mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma))
            
        last_layer_spikes = []
        for step in range(x.shape[1]):
            current_spikes = x[:, step, :]
            for i, layer in enumerate(self.layers):
                mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma = layer_states[i]
                current_spikes, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma = layer(
                    current_spikes, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma)
                layer_states[i] = (mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma)
            last_layer_spikes.append(current_spikes)
            
        embedding = torch.stack(last_layer_spikes, dim=1).sum(dim=1)
        embedding = self.dropout(embedding)
        out = self.fc_out(embedding)
        
        # 如果指定了类别数，只返回对应的输出
        if num_classes is not None:
            out = out[:, :num_classes]
            
        return out


# ==========================================================
#              6. 简化的元学习算法（基于原型网络）
# ==========================================================
class PrototypicalMetaLearner:
    """基于原型网络的元学习算法，比MAML更简单但有效"""

    def __init__(self, model, lr=0.001):
        self.model = model
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)

    def compute_prototypes(self, support_data, support_labels, num_classes):
        """计算每个类别的原型"""
        # 获取支持集的特征表示
        embeddings = self.get_embeddings(support_data)

        # 计算每个类别的原型
        prototypes = []
        for class_idx in range(num_classes):
            class_mask = (support_labels == class_idx)
            if class_mask.sum() > 0:
                class_embeddings = embeddings[class_mask]
                prototype = class_embeddings.mean(dim=0)
            else:
                prototype = torch.zeros_like(embeddings[0])
            prototypes.append(prototype)

        return torch.stack(prototypes)

    def get_embeddings(self, data):
        """获取数据的嵌入表示（不包括最后的分类层）"""
        # 初始化所有层的状态
        layer_states = []
        for layer in self.model.layers:
            mem_lif = layer.lif.init_leaky()
            v_izh, u_izh = layer.izh.init_izh()
            syn_exc_alpha, syn_inh_alpha, mem_alpha = layer.alpha.init_alpha()
            mem_soma = layer.soma_lif.init_leaky()
            layer_states.append((mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma))

        last_layer_spikes = []
        for step in range(data.shape[1]):
            current_spikes = data[:, step, :]
            for i, layer in enumerate(self.model.layers):
                mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma = layer_states[i]
                current_spikes, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma = layer(
                    current_spikes, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma)
                layer_states[i] = (mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma)
            last_layer_spikes.append(current_spikes)

        embedding = torch.stack(last_layer_spikes, dim=1).sum(dim=1)
        embedding = self.model.dropout(embedding)
        return embedding

    def meta_update(self, episode):
        """元更新：基于原型网络的损失"""
        support_data = episode['support_data']
        support_labels = episode['support_labels']
        query_data = episode['query_data']
        query_labels = episode['query_labels']
        num_classes = episode['num_classes']

        # 计算原型
        prototypes = self.compute_prototypes(support_data, support_labels, num_classes)

        # 获取查询集的嵌入
        query_embeddings = self.get_embeddings(query_data)

        # 计算距离并进行分类
        distances = torch.cdist(query_embeddings, prototypes)
        logits = -distances  # 距离越小，概率越大

        # 计算损失
        loss = F.cross_entropy(logits, query_labels)

        # 更新参数
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        return loss.item()


# ==========================================================
#              7. 数据加载工具
# ==========================================================
def load_and_window_data(data_dir, window_size, step):
    all_files = glob.glob(os.path.join(data_dir, '*.csv'))
    all_files.sort()
    all_windows, all_labels = [], []
    label_map = {os.path.basename(file).split('.csv')[0]: i for i, file in enumerate(all_files)}

    for label_name, label_idx in label_map.items():
        file_path = os.path.join(data_dir, f"{label_name}.csv")
        df = pd.read_csv(file_path)
        if df.shape[0] < window_size:
            print(f"警告: 文件 '{file_path}' 行数不足，将被跳过。")
            continue
        scaler = MinMaxScaler()
        data_scaled = scaler.fit_transform(df)
        for i in range(0, df.shape[0] - window_size + 1, step):
            all_windows.append(data_scaled[i:i + window_size, :])
            all_labels.append(label_idx)

    return np.array(all_windows), np.array(all_labels), label_map


# ==========================================================
#              8. 训练和评估函数
# ==========================================================
def meta_train_epoch(meta_learner, meta_dataloader, device):
    """元训练一个epoch"""
    total_loss = 0.0
    num_episodes = 0

    pbar = tqdm(meta_dataloader, desc="元训练中")
    for batch in pbar:
        # 由于batch_size=1，需要取出第一个episode
        episode = {key: value[0] for key, value in batch.items() if key in ['support_data', 'support_labels', 'query_data', 'query_labels']}
        episode['num_classes'] = batch['num_classes'][0]

        # 将数据移到设备上
        for key in ['support_data', 'support_labels', 'query_data', 'query_labels']:
            episode[key] = episode[key].to(device)

        # 元更新
        loss = meta_learner.meta_update(episode)
        total_loss += loss
        num_episodes += 1

        pbar.set_postfix({'Meta Loss': f'{loss:.4f}'})

    return total_loss / num_episodes


def evaluate_robustness(model, test_data, test_labels, fault_simulator, device, num_tests=100):
    """评估模型对传感器故障的鲁棒性"""
    model.eval()

    normal_correct = 0
    faulty_correct = 0
    total_samples = min(num_tests, len(test_data))

    with torch.no_grad():
        for i in range(total_samples):
            data = test_data[i:i+1].to(device)
            label = test_labels[i:i+1].to(device)

            # 正常情况下的预测
            normal_pred = model(data, num_classes=8)
            normal_correct += (normal_pred.argmax(1) == label).sum().item()

            # 故障情况下的预测
            fault_config = fault_simulator.generate_fault_config()
            faulty_data = fault_simulator.apply_faults(data, fault_config)
            faulty_pred = model(faulty_data, num_classes=8)
            faulty_correct += (faulty_pred.argmax(1) == label).sum().item()

    normal_acc = normal_correct / total_samples * 100
    faulty_acc = faulty_correct / total_samples * 100

    return normal_acc, faulty_acc


def test_different_fault_types(model, test_data, test_labels, device, num_tests=50):
    """测试不同类型故障的影响"""
    model.eval()
    fault_simulator = SensorFaultSimulator(test_data.shape[2])

    fault_types = {
        'normal': {'missing_sensors': [], 'offset_sensors': {}, 'noise_sensors': {}, 'drift_sensors': {}},
        'missing_1': {'missing_sensors': [0], 'offset_sensors': {}, 'noise_sensors': {}, 'drift_sensors': {}},
        'missing_2': {'missing_sensors': [0, 1], 'offset_sensors': {}, 'noise_sensors': {}, 'drift_sensors': {}},
        'offset': {'missing_sensors': [], 'offset_sensors': {0: 0.3}, 'noise_sensors': {}, 'drift_sensors': {}},
        'noise': {'missing_sensors': [], 'offset_sensors': {}, 'noise_sensors': {0: 2.0}, 'drift_sensors': {}},
        'drift': {'missing_sensors': [], 'offset_sensors': {}, 'noise_sensors': {}, 'drift_sensors': {0: 0.01}},
        'combined': {'missing_sensors': [1], 'offset_sensors': {0: 0.2}, 'noise_sensors': {2: 1.5}, 'drift_sensors': {}}
    }

    results = {}

    with torch.no_grad():
        for fault_name, fault_config in fault_types.items():
            correct = 0
            for i in range(min(num_tests, len(test_data))):
                data = test_data[i:i+1].to(device)
                label = test_labels[i:i+1].to(device)

                if fault_name == 'normal':
                    faulty_data = data
                else:
                    faulty_data = fault_simulator.apply_faults(data, fault_config)

                pred = model(faulty_data, num_classes=8)
                correct += (pred.argmax(1) == label).sum().item()

            accuracy = correct / min(num_tests, len(test_data)) * 100
            results[fault_name] = accuracy

    return results


# ==========================================================
#              9. 主程序
# ==========================================================
if __name__ == '__main__':
    # 参数设置
    data_dir = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\早期故障'
    window_size, step_size = 1024, 1024
    hidden_size = 128
    num_epochs = 30
    batch_size = 4  # 元学习的batch size通常较小

    # 加载数据
    print("🔄 加载数据...")
    X, y, label_map = load_and_window_data(data_dir, window_size, step_size)
    if X.shape[0] == 0:
        raise ValueError("未能加载任何有效数据。")

    print(f"✅ 数据加载完成。总窗口数: {X.shape[0]}, 标签映射: {label_map}")
    print(f"📊 传感器数量: {X.shape[2]}")

    # 划分数据集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    # 创建元学习数据集
    print("🔄 创建元学习数据集...")
    meta_dataset = MetaFaultDataset(X_train, y_train, num_sensors=X.shape[2], support_shots=3, query_shots=7)
    # 元学习通常使用batch_size=1，因为每个episode的大小可能不同
    meta_dataloader = DataLoader(meta_dataset, batch_size=1, shuffle=True, num_workers=0)

    # 创建模型和元学习器
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"🖥️  使用设备: {device}")

    model = MetaSNNClassifier(
        input_size=X.shape[2],
        hidden_size=hidden_size,
        max_num_classes=len(label_map)
    ).to(device)

    meta_learner = PrototypicalMetaLearner(model, lr=0.001)

    # 创建故障模拟器用于评估
    fault_simulator = SensorFaultSimulator(X.shape[2])

    print(f"\n🚀 开始元学习训练...")
    print(f"🎯 目标：学习对传感器故障的鲁棒性")
    print(f"📈 训练策略：原型网络 + 多种传感器故障模拟")

    # 训练循环
    best_robustness = 0
    for epoch in range(num_epochs):
        # 元训练
        meta_loss = meta_train_epoch(meta_learner, meta_dataloader, device)

        # 每5个epoch评估一次鲁棒性
        if (epoch + 1) % 5 == 0:
            test_data_tensor = torch.tensor(X_test, dtype=torch.float32)
            test_labels_tensor = torch.tensor(y_test, dtype=torch.long)

            normal_acc, faulty_acc = evaluate_robustness(
                model, test_data_tensor, test_labels_tensor, fault_simulator, device, num_tests=50
            )

            robustness_ratio = faulty_acc / normal_acc if normal_acc > 0 else 0

            print(f"Epoch [{epoch+1}/{num_epochs}] | Meta Loss: {meta_loss:.4f} | "
                  f"正常准确率: {normal_acc:.2f}% | 故障准确率: {faulty_acc:.2f}% | "
                  f"鲁棒性: {robustness_ratio:.3f}")

            # 保存最佳模型
            if robustness_ratio > best_robustness:
                best_robustness = robustness_ratio
                torch.save(model.state_dict(), 'best_meta_snn_sensor_robust.pth')
        else:
            print(f"Epoch [{epoch+1}/{num_epochs}] | Meta Loss: {meta_loss:.4f}")

    # 最终详细评估
    print("\n" + "="*60)
    print("🔍 最终详细鲁棒性评估")
    print("="*60)

    test_data_tensor = torch.tensor(X_test, dtype=torch.float32)
    test_labels_tensor = torch.tensor(y_test, dtype=torch.long)

    # 整体鲁棒性评估
    normal_acc, faulty_acc = evaluate_robustness(
        model, test_data_tensor, test_labels_tensor, fault_simulator, device, num_tests=len(X_test)
    )

    print(f"📊 整体性能:")
    print(f"   正常传感器条件下准确率: {normal_acc:.2f}%")
    print(f"   传感器故障条件下准确率: {faulty_acc:.2f}%")
    print(f"   鲁棒性保持率: {faulty_acc/normal_acc*100:.2f}%")

    # 不同故障类型的详细评估
    print(f"\n📋 不同故障类型的详细评估:")
    fault_results = test_different_fault_types(model, test_data_tensor, test_labels_tensor, device, num_tests=100)

    for fault_type, accuracy in fault_results.items():
        if fault_type == 'normal':
            print(f"   ✅ {fault_type:12}: {accuracy:.2f}% (基准)")
        else:
            degradation = fault_results['normal'] - accuracy
            print(f"   🔧 {fault_type:12}: {accuracy:.2f}% (下降 {degradation:.2f}%)")

    # 保存最终模型
    torch.save(model.state_dict(), 'final_meta_snn_sensor_robust.pth')
    print(f"\n💾 模型已保存:")
    print(f"   最佳模型: 'best_meta_snn_sensor_robust.pth'")
    print(f"   最终模型: 'final_meta_snn_sensor_robust.pth'")

    print(f"\n🎉 元学习训练完成！")
    print(f"🔬 模型已具备传感器故障鲁棒性，可在实际应用中处理:")
    print(f"   • 传感器缺失")
    print(f"   • 信号偏移")
    print(f"   • 噪声干扰")
    print(f"   • 信号漂移")
    print(f"   • 多种故障组合")
