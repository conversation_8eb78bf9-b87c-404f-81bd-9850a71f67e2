# train_multilayer_snn_epoch_v6_final.py

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import snntorch as snn
from snntorch import surrogate
from snntorch import utils

import pandas as pd
import numpy as np
import os
import glob
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from tqdm import tqdm


# --- [数据加载工具] (无变动) ---
def load_and_window_data(data_dir, window_size, step):
    all_files = glob.glob(os.path.join(data_dir, '*.csv'))
    all_files.sort()
    all_windows, all_labels = [], []
    label_map = {os.path.basename(file).split('.csv')[0]: i for i, file in enumerate(all_files)}
    for label_name, label_idx in label_map.items():
        file_path = os.path.join(data_dir, f"{label_name}.csv")
        df = pd.read_csv(file_path)
        if df.shape[0] < window_size: print(f"警告: 文件 '{file_path}' 行数不足，将被跳过。");continue
        scaler = MinMaxScaler()
        data_scaled = scaler.fit_transform(df)
        for i in range(0, df.shape[0] - window_size + 1, step): all_windows.append(
            data_scaled[i:i + window_size, :]);all_labels.append(label_idx)
    return np.array(all_windows), np.array(all_labels), label_map


class FaultDataset(Dataset):
    def __init__(self, data, labels): self.data = torch.tensor(data, dtype=torch.float32);self.labels = torch.tensor(
        labels, dtype=torch.long)

    def __len__(self): return len(self.data)

    def __getitem__(self, idx): return self.data[idx], self.labels[idx]


# ==========================================================
#      1. 恢复并“升级”Izhikevich神经元 (核心修正)
# ==========================================================
class IzhikevichNeuron(nn.Module):
    def __init__(self, a=0.02, b=0.2, c=-65, d=8, threshold=30.0, spike_grad=None):
        super().__init__()
        self.a = a
        self.b = b
        self.c = c
        self.d = d
        self.threshold = threshold

        # 使用surrogate gradient
        if spike_grad is None:
            self.spike_grad = surrogate.atan()
        else:
            self.spike_grad = spike_grad

    def init_izh(self):
        """初始化Izhikevich神经元状态"""
        return torch.zeros(1), torch.zeros(1)  # 占位符

    def forward(self, x, v=None, u=None):
        # 初始化状态（如果需要），确保在正确的设备上
        if v is None or u is None or v.device != x.device:
            v = torch.full_like(x, self.c)
            u = torch.zeros_like(x)

        # Izhikevich神经元动力学方程
        dv = 0.04 * v ** 2 + 5 * v + 140 - u + x
        du = self.a * (self.b * v - u)

        v = v + dv
        u = u + du

        # 使用surrogate gradient进行脉冲生成
        spk = self.spike_grad(v - self.threshold)

        # 重置机制
        v = torch.where(spk > 0, torch.full_like(v, self.c), v)
        u = torch.where(spk > 0, u + self.d, u)

        return spk, v, u


# ==========================================================
#              2. 多神经元层 (现在可以正确工作)
# ==========================================================
class MultiNeuronLayer(nn.Module):
    def __init__(self, input_size, hidden_size, beta_lif, beta_alpha, alpha_val):
        super().__init__()
        spike_grad = surrogate.atan()

        self.fc_lif = nn.Linear(input_size, hidden_size)
        self.lif = snn.Leaky(beta=beta_lif, spike_grad=spike_grad)

        # 使用简化的Izhikevich神经元
        self.fc_izh = nn.Linear(input_size, hidden_size)
        self.izh = IzhikevichNeuron(spike_grad=spike_grad)

        self.fc_alpha = nn.Linear(input_size, hidden_size)
        self.alpha = snn.Alpha(beta=beta_alpha, alpha=alpha_val, spike_grad=spike_grad)

        self.fusion_layer = nn.Linear(hidden_size * 3, hidden_size)
        self.soma_lif = snn.Leaky(beta=beta_lif, spike_grad=spike_grad)

    def forward(self, x, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma):
        # 处理LIF神经元
        spk_lif, mem_lif = self.lif(self.fc_lif(x), mem_lif)

        # 处理Izhikevich神经元
        spk_izh, v_izh, u_izh = self.izh(self.fc_izh(x), v_izh, u_izh)

        # 处理Alpha神经元
        spk_alpha, syn_exc_alpha, syn_inh_alpha, mem_alpha = self.alpha(
            self.fc_alpha(x), syn_exc_alpha, syn_inh_alpha, mem_alpha)

        fused_spikes = torch.cat([spk_lif, spk_izh, spk_alpha], dim=1)
        soma_current = self.fusion_layer(fused_spikes)
        output_spikes, mem_soma = self.soma_lif(soma_current, mem_soma)

        return output_spikes, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma


# ==========================================================
#              3. 多层SNN分类器 (无变动)
# ==========================================================
class MultiLayerSNNClassifier(nn.Module):
    def __init__(self, input_size, hidden_size, num_classes, num_layers=2, dropout_prob=0.3,
                 beta_lif=0.95, beta_alpha=0.8, alpha_val=0.9):
        super().__init__()
        self.layers = nn.ModuleList()
        self.layers.append(MultiNeuronLayer(input_size, hidden_size, beta_lif, beta_alpha, alpha_val))
        for _ in range(num_layers - 1):
            self.layers.append(MultiNeuronLayer(hidden_size, hidden_size, beta_lif, beta_alpha, alpha_val))
        self.dropout = nn.Dropout(dropout_prob)
        self.fc_out = nn.Linear(hidden_size, num_classes)

    def forward(self, x):
        # 初始化所有层的状态
        layer_states = []
        for layer in self.layers:
            mem_lif = layer.lif.init_leaky()
            v_izh, u_izh = layer.izh.init_izh()
            syn_exc_alpha, syn_inh_alpha, mem_alpha = layer.alpha.init_alpha()
            mem_soma = layer.soma_lif.init_leaky()
            layer_states.append((mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma))

        last_layer_spikes = []
        for step in range(x.shape[1]):
            current_spikes = x[:, step, :]
            for i, layer in enumerate(self.layers):
                mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma = layer_states[i]
                current_spikes, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma = layer(
                    current_spikes, mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma)
                layer_states[i] = (mem_lif, v_izh, u_izh, syn_exc_alpha, syn_inh_alpha, mem_alpha, mem_soma)
            last_layer_spikes.append(current_spikes)
        embedding = torch.stack(last_layer_spikes, dim=1).sum(dim=1)
        embedding = self.dropout(embedding)
        out = self.fc_out(embedding)
        return out


# --- [训练与评估函数] (无变动) ---
def train_epoch(model, train_loader, optimizer, criterion, device):
    model.train()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0
    pbar = tqdm(train_loader, desc="训练中")
    for data, labels in pbar:
        data, labels = data.to(device), labels.to(device)
        outputs = model(data)
        loss = criterion(outputs, labels)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        running_loss += loss.item() * data.size(0)
        _, predicted = torch.max(outputs.data, 1)
        total_samples += labels.size(0)
        correct_predictions += (predicted == labels).sum().item()
    return running_loss / total_samples, 100 * correct_predictions / total_samples


def evaluate_model(model, test_loader, criterion, device):
    model.eval()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0
    with torch.no_grad():
        for data, labels in test_loader:
            data, labels = data.to(device), labels.to(device)
            outputs = model(data)
            loss = criterion(outputs, labels)
            running_loss += loss.item() * data.size(0)
            _, predicted = torch.max(outputs.data, 1)
            total_samples += labels.size(0)
            correct_predictions += (predicted == labels).sum().item()
    return running_loss / total_samples, 100 * correct_predictions / total_samples


# ==========================================================
#                        4. 主程序 (无变动)
# ==========================================================
if __name__ == '__main__':
    data_dir = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_20mm02hz全周期故障\早期故障'
    model_save_path = 'multilayer_snn_classifier.pth'
    window_size, step_size = 1024, 1024
    hidden_size = 128
    learning_rate = 1e-3
    num_epochs = 60
    batch_size = 32
    dropout_prob = 0.3
    num_layers = 2
    beta_lif = 0.95
    beta_alpha = 0.85
    alpha_val = 0.9
    X, y, label_map = load_and_window_data(data_dir, window_size, step_size)
    if X.shape[0] == 0: raise ValueError("未能加载任何有效数据。")
    print(f"数据加载完成。总窗口数: {X.shape[0]}, 标签映射: {label_map}")
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    train_dataset = FaultDataset(X_train, y_train)
    test_dataset = FaultDataset(X_test, y_test)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    input_size = X.shape[2]
    num_classes = len(label_map)
    model = MultiLayerSNNClassifier(input_size=input_size, hidden_size=hidden_size, num_classes=num_classes,
                                    num_layers=num_layers, dropout_prob=dropout_prob, beta_lif=beta_lif,
                                    beta_alpha=beta_alpha, alpha_val=alpha_val).to(device)
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    print(f"\n开始训练 {num_layers}-层 多神经元SNN...")
    for epoch in range(num_epochs):
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        test_loss, test_acc = evaluate_model(model, test_loader, criterion, device)
        print(
            f"Epoch [{epoch + 1}/{num_epochs}] | 训练损失: {train_loss:.4f}, 训练准确率: {train_acc:.2f}% | 测试损失: {test_loss:.4f}, 测试准确率: {test_acc:.2f}%")
    torch.save(model.state_dict(), model_save_path)
    print(f"\n训练完成。模型权重已保存至: '{model_save_path}'")