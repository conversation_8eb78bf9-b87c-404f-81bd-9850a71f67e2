import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from spikingjelly.activation_based import neuron

# 1. 数据集类
class FaultDataset(Dataset):
    def __init__(self, root_dir, seq_len=128, normalize=True, window_step=32):
        self.samples = []
        self.labels = []
        self.label_map = {}
        label_idx = 0
        for fname in os.listdir(root_dir):
            if not fname.endswith('.csv'):
                continue
            fpath = os.path.join(root_dir, fname)
            data = pd.read_csv(fpath, header=None, skiprows=1).values
            # 滑动窗口分割
            for i in range(0, len(data) - seq_len + 1, window_step):
                sample = data[i:i+seq_len]
                sample = sample.astype(np.float32)  # 强制类型转换
                if normalize:
                    sample = (sample - np.mean(sample, axis=0)) / (np.std(sample, axis=0) + 1e-6)
                self.samples.append(sample)
                if fname not in self.label_map:
                    self.label_map[fname] = label_idx
                    label_idx += 1
                self.labels.append(self.label_map[fname])
        self.samples = np.array(self.samples, dtype=np.float32)
        self.labels = np.array(self.labels, dtype=np.int64)

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        x = self.samples[idx]
        x = torch.tensor(x).unsqueeze(0).unsqueeze(0)  # [1, 1, seq_len, feature]
        y = torch.tensor(self.labels[idx])
        return x, y

# 2. MLP模块
class MLP(nn.Module):
    def __init__(self, in_features, hidden_features, drop=0.):
        super().__init__()
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = nn.GELU()
        self.fc2 = nn.Linear(hidden_features, in_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x

# 3. SSA和Block模块
import torch
import torch.nn as nn
import numpy as np
from spikingjelly.activation_based import neuron

class SSA(nn.Module):
    def __init__(self, dim, num_heads=7, qkv_bias=False, qk_scale=None, attn_drop=0., proj_drop=0., sr_ratio=1):
        super().__init__()
        assert dim % num_heads == 0, f"dim {dim} should be divided by num_heads {num_heads}."
        self.dim = dim
        self.num_heads = num_heads
        self.scale = 0.125
        self.q_linear = nn.Linear(dim, dim)
        self.q_bn = nn.BatchNorm1d(dim)
        self.q_lif = neuron.LIFNode()
        self.k_linear = nn.Linear(dim, dim)
        self.k_bn = nn.BatchNorm1d(dim)
        self.k_lif = neuron.LIFNode()

        self.v_linear = nn.Linear(dim, dim)
        self.v_bn = nn.BatchNorm1d(dim)
        self.v_lif = neuron.LIFNode()

        self.attn_lif = neuron.LIFNode()

        self.proj_linear = nn.Linear(dim, dim)
        self.proj_bn = nn.BatchNorm1d(dim)
        self.proj_lif = neuron.LIFNode()

    def forward(self, x):
        T,B,N,C = x.shape

        x_for_qkv = x.flatten(0, 1)  # TB, N, C
        q_linear_out = self.q_linear(x_for_qkv)  # [TB, N, C]
        q_linear_out = self.q_bn(q_linear_out. transpose(-1, -2)).transpose(-1, -2).reshape(T, B, N, C).contiguous()
        q_linear_out = self.q_lif(q_linear_out)
        q = q_linear_out.reshape(T, B, N, self.num_heads, C//self.num_heads).permute(0, 1, 3, 2, 4).contiguous()

        k_linear_out = self.k_linear(x_for_qkv)
        k_linear_out = self.k_bn(k_linear_out. transpose(-1, -2)).transpose(-1, -2).reshape(T, B, N, C).contiguous()
        k_linear_out = self.k_lif(k_linear_out)
        k = k_linear_out.reshape(T, B, N, self.num_heads, C//self.num_heads).permute(0, 1, 3, 2, 4).contiguous()

        v_linear_out = self.v_linear(x_for_qkv)
        v_linear_out = self.v_bn(v_linear_out. transpose(-1, -2)).transpose(-1, -2).reshape(T, B, N, C).contiguous()
        v_linear_out = self.v_lif(v_linear_out)
        v = v_linear_out.reshape(T, B, N, self.num_heads, C//self.num_heads).permute(0, 1, 3, 2, 4).contiguous()

        attn = (q @ k.transpose(-2, -1)) * self.scale

        x = attn @ v
        x = x.transpose(2, 3).reshape(T, B, N, C).contiguous()
        x = self.attn_lif(x)
        x = x.flatten(0, 1)
        x = self.proj_lif(self.proj_bn(self.proj_linear(x).transpose(-1, -2)).transpose(-1, -2).reshape(T, B, N, C))

        return x

class Block(nn.Module):
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, qk_scale=None, drop=0., attn_drop=0.,
                drop_path=0., norm_layer=nn.LayerNorm, sr_ratio=1):
        super().__init__()
        self.attn = SSA(dim, num_heads=num_heads, qkv_bias=qkv_bias, qk_scale=qk_scale,
                            attn_drop=attn_drop, proj_drop=drop, sr_ratio=sr_ratio)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = MLP(in_features=dim, hidden_features=mlp_hidden_dim, drop=drop)

    def forward(self, x):
        x = x + self.attn(x)
        x = x + self.mlp(x)
        return x

# 4. Transformer模型
class SpikingTransformer(nn.Module):
    def __init__(self, seq_len, feature_dim, num_classes, num_layers=2, num_heads=7, mlp_ratio=4.):
        super().__init__()
        self.blocks = nn.ModuleList([
            Block(dim=feature_dim, num_heads=num_heads, mlp_ratio=mlp_ratio)
            for _ in range(num_layers)
        ])
        self.norm = nn.LayerNorm(feature_dim)
        self.head = nn.Linear(feature_dim, num_classes)

    def forward(self, x):
        # x: [T, B, N, C]
        for blk in self.blocks:
            x = blk(x)
        x = self.norm(x)
        # 取最后一个时间步/平均池化
        x = x.mean(dim=2)  # [T, B, C]
        x = x.mean(dim=0)  # [B, C]
        out = self.head(x)
        return out

# 5. 训练与测试主流程
def train():
    # 数据路径
    data_dir = r'D:\PyCharm\SNN\EHA_data\sin信号\csv数据_10mm01hz全周期数据\前期故障'
    seq_len = 128
    feature_dim = 7  # 假设每个csv只有一列
    num_classes = 8  # 根据实际类别数调整
    window_step = 512  # 滑动窗口步长，可自定义

    train_dataset = FaultDataset(data_dir, seq_len=seq_len, normalize=True, window_step=window_step)
    test_dataset = FaultDataset(data_dir, seq_len=seq_len, normalize=True, window_step=window_step)
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False)

    model = SpikingTransformer(seq_len=seq_len, feature_dim=feature_dim, num_classes=num_classes)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=1e-3)

    for epoch in range(20):
        model.train()
        for x, y in train_loader:
            x, y = x.to(device), y.to(device)
            out = model(x)
            loss = criterion(out, y)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        print(f'Epoch {epoch+1}, Loss: {loss.item():.4f}')

        # 测试
        model.eval()
        correct = 0
        total = 0
        with torch.no_grad():
            for x, y in test_loader:
                x, y = x.to(device), y.to(device)
                out = model(x)
                pred = out.argmax(dim=1)
                correct += (pred == y).sum().item()
                total += y.size(0)
        print(f'Test Acc: {correct/total:.4f}')

if __name__ == '__main__':
    train()
